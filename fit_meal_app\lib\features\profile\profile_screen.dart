import 'package:flutter/material.dart';
import 'package:fit_meal_app/features/profile/widgets/profile_header.dart';
import 'package:fit_meal_app/features/profile/widgets/stats_section.dart';
import 'package:fit_meal_app/features/profile/widgets/goal_section.dart';
import 'package:fit_meal_app/features/profile/widgets/macronutrients_section.dart';
import 'package:fit_meal_app/core/utils/size_config.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:fit_meal_app/features/auth/services/auth_service.dart';
import 'package:fit_meal_app/shared/widgets/app_toast.dart';
import 'package:go_router/go_router.dart';

class ProfileScreen extends ConsumerWidget {
  const ProfileScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    SizeConfig().init(context);
    return Scaffold(
      backgroundColor: const Color(0xFFFCFCFC),
      appBar: const ProfileHeader(),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(20),
        child: Column(
          children: [
            const SizedBox(height: 20),
            const StatsSection(),
            const SizedBox(height: 30),
            const GoalSection(),
            const SizedBox(height: 30),
            const MacronutrientsSection(),
            const SizedBox(height: 40),
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: () async {
                  try {
                    await ref.read(authServiceProvider).signOut();
                    if (context.mounted) {
                      context.go('/login');
                      showAppToast(
                        context: context,
                        message: "Successfully logged out.",
                        type: ToastType.success,
                      );
                    }
                  } catch (e) {
                    if (context.mounted) {
                      showAppToast(
                        context: context,
                        message: "Error logging out: $e",
                        type: ToastType.error,
                      );
                    }
                  }
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.redAccent,
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(30),
                  ),
                ),
                child: const Text(
                  'Logout',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
