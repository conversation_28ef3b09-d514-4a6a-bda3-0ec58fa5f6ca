import 'package:fit_meal_app/features/auth/services/auth_service.dart';
import 'package:fit_meal_app/features/auth/widgets/custom_text_field.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:fit_meal_app/shared/widgets/app_toast.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:fit_meal_app/core/utils/size_config.dart';

class ForgotPasswordScreen extends ConsumerStatefulWidget {
  const ForgotPasswordScreen({super.key});

  @override
  ConsumerState<ForgotPasswordScreen> createState() =>
      _ForgotPasswordScreenState();
}

class _ForgotPasswordScreenState extends ConsumerState<ForgotPasswordScreen> {
  final _emailController = TextEditingController();
  bool _isLoading = false;

  @override
  void dispose() {
    _emailController.dispose();
    super.dispose();
  }

  Future<void> _sendResetEmail() async {
    setState(() {
      _isLoading = true;
    });

    try {
      await ref
          .read(authServiceProvider)
          .sendPasswordResetEmail(_emailController.text.trim());
      if (mounted) {
        showAppToast(
          context: context,
          message: 'Password reset link sent! Please check your email.',
          type: ToastType.success,
        );
        context.pop();
      }
    } on AuthException catch (e) {
      if (mounted) {
        showAppToast(
          context: context,
          message: e.message,
          type: ToastType.error,
        );
      }
    } catch (e) {
      if (mounted) {
        showAppToast(
          context: context,
          message: 'An unexpected error occurred. Please try again.',
          type: ToastType.error,
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    SizeConfig().init(context);
    return Scaffold(
      backgroundColor: const Color(0xFFFCFCFC),
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        leading: IconButton(
          icon: Image.asset(
            'assets/icons/left-arrow.png',
            width: 16,
            height: 16,
          ),
          onPressed:
              () => context.canPop() ? context.pop() : context.go('/login'),
        ),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.symmetric(horizontal: 20.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            SizedBox(height: SizeConfig.getPropHeight(20)),
            const Text(
              'Forgot Password',
              style: TextStyle(
                fontFamily: 'Montserrat',
                fontSize: 20,
                fontWeight: FontWeight.w700,
                color: Color(0xFF191919),
              ),
            ),
            SizedBox(height: SizeConfig.getPropHeight(8)),
            const Text(
              'Please enter your email below to receive \nyour password reset code.',
              style: TextStyle(
                fontFamily: 'Montserrat',
                fontSize: 14,
                fontWeight: FontWeight.w500,
                color: Color(0xFF3A4750),
              ),
            ),
            SizedBox(height: SizeConfig.getPropHeight(40)),
            CustomTextField(
              controller: _emailController,
              labelText: 'Email',
              hintText: '<EMAIL>',
            ),
            SizedBox(height: SizeConfig.getPropHeight(60)),
            ElevatedButton(
              onPressed: _isLoading ? null : _sendResetEmail,
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFF00ADB5),
                minimumSize: const Size(double.infinity, 55),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(6),
                ),
              ),
              child:
                  _isLoading
                      ? const CircularProgressIndicator(color: Colors.white)
                      : const Text(
                        'Reset Password',
                        style: TextStyle(
                          color: Colors.white,
                          fontFamily: 'Montserrat',
                          fontWeight: FontWeight.w600,
                          fontSize: 18,
                        ),
                      ),
            ),
          ],
        ),
      ),
    );
  }
}
