import 'package:flutter/material.dart';

class AdditionalExerciseSection extends StatelessWidget {
  const AdditionalExerciseSection({super.key});

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            const Text(
              'Additional Exercise',
              style: TextStyle(
                fontFamily: 'Montserrat',
                fontSize: 16,
                fontWeight: FontWeight.w700,
                color: Color(0xFF191919),
              ),
            ),
            TextButton(
              onPressed: () {},
              style: TextButton.styleFrom(
                padding: EdgeInsets.zero,
                minimumSize: Size.zero,
                tapTargetSize: MaterialTapTargetSize.shrinkWrap,
              ),
              child: const Text(
                'See all',
                style: TextStyle(
                  fontFamily: 'Montserrat',
                  fontSize: 12,
                  fontWeight: FontWeight.w500,
                  color: Color(0xFF00ADB5),
                ),
              ),
            ),
          ],
        ),
        const SizedBox(height: 12),
        AdditionalExerciseCard(
          title: 'Exercises with Jumping \nRope',
          level: 'Beginner',
          duration: '10 min',
          calories: '110 kcal',
          imageUrl:
              'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b',
        ),
        const SizedBox(height: 12),
        const Divider(color: Color(0xFFE5E5E5), thickness: 1, height: 1),
        const SizedBox(height: 12),
        AdditionalExerciseCard(
          title: 'Exercises with Holding Jumping \nRope ',
          level: 'Beginner',
          duration: '8 min',
          calories: '135 kcal',
          imageUrl:
              'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b',
        ),
        const SizedBox(height: 12),
        const Divider(color: Color(0xFFE5E5E5), thickness: 1, height: 1),
        const SizedBox(height: 12),
        AdditionalExerciseCard(
          title: 'Exercises with Sitting \nDumbbells',
          level: 'Beginner',
          duration: '5 min',
          calories: '135 kcal',
          imageUrl:
              'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b',
        ),
      ],
    );
  }
}

class AdditionalExerciseCard extends StatelessWidget {
  final String title;
  final String level;
  final String duration;
  final String calories;
  final String imageUrl;

  const AdditionalExerciseCard({
    super.key,
    required this.title,
    required this.level,
    required this.duration,
    required this.calories,
    required this.imageUrl,
  });

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: 100,
      child: Row(
        children: [
          Container(
            width: 100,
            height: 100,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(12),
              image: DecorationImage(
                image: NetworkImage(imageUrl),
                fit: BoxFit.cover,
              ),
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                const SizedBox(height: 8),
                Text(
                  title,
                  style: const TextStyle(
                    fontFamily: 'Montserrat',
                    fontSize: 13,
                    fontWeight: FontWeight.w600,
                    color: Color(0xFF191919),
                    height: 1.22,
                  ),
                ),
                const SizedBox(height: 8),
                Row(
                  children: [
                    const Icon(
                      Icons.local_fire_department,
                      size: 14,
                      color: Color(0xFF00ADB5),
                    ),
                    const SizedBox(width: 4),
                    Text(
                      calories,
                      style: const TextStyle(
                        fontFamily: 'Montserrat',
                        fontSize: 12,
                        fontWeight: FontWeight.w500,
                        color: Color(0xFF3A4750),
                      ),
                    ),
                    const SizedBox(width: 8),
                    Container(
                      width: 1,
                      height: 12,
                      color: const Color(0xFF3A4750),
                    ),
                    const SizedBox(width: 8),
                    const Icon(
                      Icons.access_time,
                      size: 14,
                      color: Color(0xFF00ADB5),
                    ),
                    const SizedBox(width: 4),
                    Text(
                      duration,
                      style: const TextStyle(
                        fontFamily: 'Montserrat',
                        fontSize: 12,
                        fontWeight: FontWeight.w500,
                        color: Color(0xFF3A4750),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 6),
                Text(
                  level,
                  style: const TextStyle(
                    fontFamily: 'Montserrat',
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                    color: Color(0xFF303841),
                  ),
                ),
                const SizedBox(height: 8),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
