import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:fit_meal_app/core/router/app_router.dart';
import 'package:fit_meal_app/core/theme/app_theme.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:flutter_libphonenumber/flutter_libphonenumber.dart';

// Global initialization state
final heavyServicesInitialized = StateProvider<bool>((ref) => false);

Future<void> main() async {
  if (kDebugMode) {
    // Track startup time in debug mode
    final stopwatch = Stopwatch()..start();
    
    WidgetsFlutterBinding.ensureInitialized();
    
    // Track first frame render time
    WidgetsBinding.instance.addPostFrameCallback((_) {
      stopwatch.stop();
      debugPrint('🚀 App startup time: ${stopwatch.elapsedMilliseconds}ms');
      
      // Log device info safely
      try {
        final view = WidgetsBinding.instance.platformDispatcher.views.first;
        debugPrint('📱 Screen size: ${view.physicalSize / view.devicePixelRatio}');
        debugPrint('📦 Device pixel ratio: ${view.devicePixelRatio}');
      } catch (e) {
        debugPrint('📊 Device info logging skipped: $e');
      }
    });
  } else {
    WidgetsFlutterBinding.ensureInitialized();
  }
  
  // Only load critical environment variables
  await dotenv.load(fileName: ".env");
  
  // Start the app immediately with minimal initialization
  runApp(const ProviderScope(child: MyApp()));
  
  // Initialize heavy services in the background
  _initializeHeavyServicesAsync();
}

// Non-blocking initialization of heavy services
void _initializeHeavyServicesAsync() async {
  try {
    await Future.wait([
      Supabase.initialize(
        url: dotenv.env['SUPABASE_URL']!,
        anonKey: dotenv.env['SUPABASE_ANON_KEY']!,
      ),
      init(), // Phone number library
      // Add other heavy initializations here
    ]);
    
    // Update global state when services are ready
    debugPrint('🔧 Heavy services initialized successfully');
  } catch (e) {
    debugPrint('❌ Error initializing heavy services: $e');
  }
}

class MyApp extends ConsumerStatefulWidget {
  const MyApp({super.key});

  @override
  ConsumerState<MyApp> createState() => _MyAppState();
}

class _MyAppState extends ConsumerState<MyApp> {
  @override
  void initState() {
    super.initState();
    _setupAuthListener();
  }

  void _setupAuthListener() {
    // Wrap in try-catch since Supabase might not be initialized yet
    try {
      Supabase.instance.client.auth.onAuthStateChange.listen((data) {
        final session = data.session;
        final event = data.event;
        if (event == AuthChangeEvent.passwordRecovery) {
          final router = ref.read(routerProvider);
          router.go('/reset-password');
        } else if (session == null) {
          final router = ref.read(routerProvider);
          if (router.routerDelegate.currentConfiguration.fullPath != '/login' && 
              router.routerDelegate.currentConfiguration.fullPath != '/signup') {
             router.go('/login');
          }
        }
      });
    } catch (e) {
      // Services not ready yet, will be handled when they are
      debugPrint('⏳ Auth listener setup deferred: $e');
      
      // Retry auth listener setup after a short delay
      Future.delayed(const Duration(milliseconds: 500), () {
        _setupAuthListener();
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final router = ref.watch(routerProvider);

    return MaterialApp.router(
      title: 'FitMeal',
      theme: AppTheme.lightTheme,
      routerConfig: router,
      debugShowCheckedModeBanner: false,
      // Add performance overlay in debug mode
      showPerformanceOverlay: kDebugMode && const bool.fromEnvironment('SHOW_PERFORMANCE'),
    );
  }
}
