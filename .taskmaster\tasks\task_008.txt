# Task ID: 8
# Title: <PERSON><PERSON><PERSON><PERSON><PERSON> (Dashboard & Progress)
# Status: pending
# Dependencies: 2, 3, 4, 5, 6
# Priority: medium
# Description: Create a unified dashboard screen that visualizes the user's fitness journey. This screen will display key metrics, progress charts over time, and summaries of completed workouts.
# Details:
This task involves creating a new screen, likely serving as the 'Home' tab's main view, to act as the user's progress dashboard. Use a `StreamProvider` from `flutter_riverpod` to fetch and listen for real-time updates from the `progress` table in Supabase for the currently authenticated user (`auth.uid()`). The screen should also fetch the user's initial metrics (e.g., starting weight) collected during the onboarding flow (Task 5). Implement UI components based on Figma designs, including: 1) Key Metric Cards for displaying current weight, total workouts, and other summary stats. 2) Progress Charts, using a library like `fl_chart`, to visualize metrics like weight change over time. 3) A 'Recent Activity' list showing the last few completed workouts, linking to their respective results screens (from Task 6). The logic should handle calculations for derived data, such as total change in weight.

# Test Strategy:
1. **Widget Testing**: Create widget tests for the dashboard screen using a mock Riverpod provider. Test the loading, error, and empty states (for new users with no data). Provide mock progress data and verify that metric cards and charts render the correct values. 2. **Integration Testing**: Write an integration test that simulates a user logging in, completing a workout (simulating the outcome of Task 6), and then navigating to the dashboard to confirm that the new progress data is displayed correctly and updates the charts. 3. **Manual Testing**: Manually complete several different workouts and verify the dashboard updates in real-time. Check that all calculations are correct and that the UI is visually consistent with the Figma designs in both light and dark modes.
