# Task ID: 7
# Title: Implement Meal Plan Details Screen
# Status: pending
# Dependencies: 2, 3, 4
# Priority: medium
# Description: Implement the user interface for the Meal Plan Details screen, which will display a comprehensive breakdown of a selected meal plan, including daily meals and nutritional information.
# Details:
This task involves creating a new screen that fetches and displays the details of a specific meal plan from Supabase. The screen should be accessible via `go_router` and accept a `meal_plan_id` as a route parameter. Use a `FutureProvider` from `flutter_riverpod` to asynchronously fetch the meal plan data, including all associated meals for each day of the week, from the `meal_plans` and `meals` tables. The UI, based on the Figma design, should present the information in a clear, organized manner, likely using a combination of `ListView`, `ExpansionTile` for each day, and custom widgets for individual meal cards (e.g., Breakfast, Lunch, Dinner). Each meal card should display its name, ingredients, and key nutritional information like calories, protein, carbs, and fat.

# Test Strategy:
1. **Widget Testing**: Create widget tests for the Meal Plan Details screen using mock data. Verify that the screen correctly displays loading and error states. Test that when data is successfully provided, the meal plan title, daily sections, and individual meal cards are rendered correctly with all expected nutritional information. 2. **Integration Testing**: Write an integration test that simulates navigating from a meal plan list to the details screen. This test should use a real Supabase backend (or a mocked Supabase client) to confirm that the correct data is fetched and displayed for a given `meal_plan_id`. 3. **Manual Testing**: Manually navigate to the screen on a device/emulator. Verify the layout matches the Figma design across different screen sizes. Check for visual consistency with the application's theme (light and dark modes). Ensure scrolling is smooth and all interactive elements are responsive.
