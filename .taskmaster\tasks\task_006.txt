# Task ID: 6
# Title: Workout Core: Exercise Details, Live Training, and Results Screens
# Status: pending
# Dependencies: 2, 3, 4, 5
# Priority: high
# Description: Develop the core workout loop, including screens for viewing exercise details, an active training session with timers, and a results summary screen that saves progress to Supabase.
# Details:
This task involves creating the three key screens for the workout experience. First, the 'Exercise Details' screen will fetch and display information for a specific exercise from the Supabase `exercises` table, including instructions and a visual guide (GIF/video). Second, the 'Live Training' screen will manage the active workout session. It will use a state management solution like Riverpod to track the current exercise, sets, reps, and control work/rest timers. Navigation controls (next/previous exercise) should be included. Third, upon completion, the 'Results' screen will summarize the session (duration, total volume, etc.) and persist this data by creating a new entry in the `progress` table in Supabase, associated with the authenticated user's ID.

# Test Strategy:
1. **Widget Tests**: Create widget tests for the 'Exercise Details' screen with mock data. Test the timer logic on the 'Live Training' screen independently. Verify the 'Results' screen correctly displays summary data passed to it. 2. **Integration Test**: Write a test that simulates a full workout flow: starting a workout, proceeding through at least two exercises and a rest period, finishing the workout, and verifying that the app navigates to the Results screen. 3. **Manual E2E Test**: Log in as a test user, select and start a workout. Complete the session and confirm the summary on the 'Results' screen is accurate. Finally, query the Supabase `progress` table directly to ensure a new record was successfully created with the correct `user_id` and session data.
