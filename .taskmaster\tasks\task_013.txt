# Task ID: 13
# Title: <PERSON><PERSON><PERSON> <PERSON>ş Profil Yönetimi
# Status: pending
# Dependencies: 2, 3, 4, 5, 9, 12
# Priority: low
# Description: Implement the App Settings and Edit Profile screens, allowing users to manage application preferences like theme and notifications, and to update their personal profile information.
# Details:
Create two new screens accessible from the main Profile tab. 1. **Edit Profile Screen**: This screen will allow authenticated users to modify their personal data. It should fetch the current user's profile data (name, age, weight, height, fitness level, goal) from the Supabase `profiles` table, which was initially populated during onboarding (Task 5). Use a `Form` widget with appropriate input fields. Upon saving, use a Riverpod provider to call `supabase.from('profiles').update({...})` with the new data, targeting the current user's ID. 2. **Settings Screen**: This screen will serve as a hub for application-level configurations. It should include: a toggle/selector for theme (Light/Dark/System) that updates the app's `ThemeData` via a provider; a navigation link to 'Notification Preferences' (from Task 12); a navigation link to 'Manage Subscription' (from Task 9); and a 'Logout' button that calls `supabase.auth.signOut()` and navigates the user back to the login screen (Task 4).

# Test Strategy:
1. **Widget Testing**: Create widget tests for the 'Edit Profile' screen using a mock Riverpod provider to pre-fill the form with user data. Verify that input changes are captured and that the `update` method is called with the correct payload on save. For the 'Settings' screen, test that tapping the theme selector updates the theme state and that the 'Logout' button triggers the sign-out function. Use mock navigation to verify that links to 'Notifications' and 'Subscription' work correctly. 2. **Integration Testing**: Manually test the end-to-end flow. Navigate to 'Edit Profile', change a value like weight, save it, then navigate to the Dashboard (Task 8) to confirm the updated value is displayed. Test the 'Logout' button to ensure it redirects to the login screen. Verify that changing the theme in settings applies the new theme across the entire application.
