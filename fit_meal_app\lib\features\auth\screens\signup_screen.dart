import 'package:flutter/material.dart';
import 'package:fit_meal_app/features/auth/widgets/custom_text_field.dart';
import 'package:fit_meal_app/features/auth/widgets/social_login_button.dart';
import 'package:go_router/go_router.dart';
import 'package:fit_meal_app/features/auth/services/auth_service.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:fit_meal_app/shared/widgets/app_toast.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:fit_meal_app/features/auth/widgets/password_policy_checker.dart';
import 'package:country_picker/country_picker.dart';
import 'package:flutter_libphonenumber/flutter_libphonenumber.dart';
import 'dart:async';
import 'package:fit_meal_app/core/utils/size_config.dart';

class SignupScreen extends ConsumerStatefulWidget {
  const SignupScreen({super.key});

  @override
  ConsumerState<SignupScreen> createState() => _SignupScreenState();
}

class _SignupScreenState extends ConsumerState<SignupScreen> {
  final _fullNameController = TextEditingController();
  final _phoneController = TextEditingController();
  final _emailController = TextEditingController();
  final _passwordController = TextEditingController();
  bool _isLoading = false;
  bool _has8Chars = false;
  bool _hasUppercase = false;
  bool _hasLowercase = false;
  bool _hasNumber = false;
  bool _isEmailDomainValid = true;
  final bool _isPhoneValid = true;
  CountryWithPhoneCode? _selectedCountryFormatter;

  @override
  void initState() {
    super.initState();
    _passwordController.addListener(_validatePassword);
    _emailController.addListener(_validateEmailDomain);
    _setCountryFormatter(Country.parse('TR'));
  }

  void _setCountryFormatter(Country country) {
    final countryData = CountryManager().countries.firstWhere(
      (c) => c.countryCode == country.countryCode,
      orElse: () => CountryManager().countries.first, // Fallback
    );
    setState(() {
      _selectedCountryFormatter = countryData;
    });
  }

  @override
  void dispose() {
    _fullNameController.dispose();
    _phoneController.dispose();
    _emailController.removeListener(_validateEmailDomain);
    _emailController.dispose();
    _passwordController.removeListener(_validatePassword);
    _passwordController.dispose();
    super.dispose();
  }

  void _validateEmailDomain() {
    final email = _emailController.text;
    if (email.contains('@')) {
      final validDomains = [
        'gmail.com',
        'yahoo.com',
        'outlook.com',
        'hotmail.com',
        'icloud.com',
      ];
      final emailDomain = email.split('@').last;
      if (mounted) {
        setState(() {
          _isEmailDomainValid = validDomains.contains(emailDomain);
        });
      }
    } else if (mounted) {
      setState(() {
        _isEmailDomainValid = true; // Don't show error until '@' is typed
      });
    }
  }

  void _validatePassword() {
    final password = _passwordController.text;
    if (mounted) {
      setState(() {
        _has8Chars = password.length >= 8;
        _hasUppercase = password.contains(RegExp(r'[A-Z]'));
        _hasLowercase = password.contains(RegExp(r'[a-z]'));
        _hasNumber = password.contains(RegExp(r'[0-9]'));
      });
    }
  }

  Future<void> _signUp() async {
    setState(() {
      _isLoading = true;
    });

    try {
      await ref
          .read(authServiceProvider)
          .signUpWithPassword(
            _emailController.text.trim(),
            _passwordController.text.trim(),
            fullName: _fullNameController.text.trim(),
            phone: _phoneController.text.trim(),
            isoCode: _selectedCountryFormatter?.countryCode ?? '',
          );
      if (mounted) {
        showAppToast(
          context: context,
          message: 'Verification email sent! Please check your inbox.',
          type: ToastType.success,
        );
        context.go('/login');
      }
    } on AuthException catch (e) {
      if (mounted) {
        showAppToast(
          context: context,
          message: e.message,
          type: ToastType.error,
        );
      }
    } catch (e) {
      if (mounted) {
        showAppToast(
          context: context,
          message: 'An unexpected error occurred. Please try again.',
          type: ToastType.error,
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    SizeConfig().init(context); // Initialize SizeConfig
    return Scaffold(
      backgroundColor: const Color(0xFFFCFCFC),
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        leading: IconButton(
          icon: Image.asset(
            'assets/icons/left-arrow.png',
            width: 16,
            height: 16,
          ),
          onPressed:
              () => context.canPop() ? context.pop() : context.go('/login'),
        ),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.symmetric(horizontal: 20.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            SizedBox(height: SizeConfig.getPropHeight(20)),
            const Text(
              'Create Accounts',
              style: TextStyle(
                fontFamily: 'Montserrat',
                fontSize: 20,
                fontWeight: FontWeight.w700,
                color: Color(0xFF191919),
              ),
            ),
            SizedBox(height: SizeConfig.getPropHeight(8)),
            const Text(
              'Please enter your credentials to \nproceed',
              style: TextStyle(
                fontFamily: 'Montserrat',
                fontSize: 14,
                fontWeight: FontWeight.w500,
                color: Color(0xFF3A4750),
              ),
            ),
            SizedBox(height: SizeConfig.getPropHeight(40)),
            CustomTextField(
              controller: _fullNameController,
              labelText: 'Full Name',
              hintText: 'John welles',
            ),
            SizedBox(height: SizeConfig.getPropHeight(20)),
            CustomTextField(
              controller: _phoneController,
              labelText: 'Phone',
              hintText: '************',
              isPhone: true,
              phoneFormatter:
                  _selectedCountryFormatter != null
                      ? LibPhonenumberTextFormatter(
                        country: _selectedCountryFormatter!,
                      )
                      : null,
              onCountryChanged: (country) {
                _setCountryFormatter(country);
              },
            ),
            if (!_isPhoneValid)
              Padding(
                padding: EdgeInsets.only(top: SizeConfig.getPropHeight(8)),
                child: const Text(
                  'Please enter a valid phone number for the selected country.',
                  style: TextStyle(color: Colors.red, fontSize: 12),
                ),
              ),
            SizedBox(height: SizeConfig.getPropHeight(20)),
            CustomTextField(
              controller: _emailController,
              labelText: 'Email address',
              hintText: '<EMAIL>',
            ),
            if (!_isEmailDomainValid)
              Padding(
                padding: EdgeInsets.only(top: SizeConfig.getPropHeight(8)),
                child: const Text(
                  'Please use a valid email provider (e.g., Gmail, Outlook).',
                  style: TextStyle(color: Colors.red, fontSize: 12),
                ),
              ),
            SizedBox(height: SizeConfig.getPropHeight(20)),
            CustomTextField(
              controller: _passwordController,
              labelText: 'Password',
              hintText: '******',
              isPassword: true,
            ),
            SizedBox(height: SizeConfig.getPropHeight(16)),
            PasswordPolicyChecker(
              has8Chars: _has8Chars,
              hasUppercase: _hasUppercase,
              hasLowercase: _hasLowercase,
              hasNumber: _hasNumber,
            ),
            SizedBox(height: SizeConfig.getPropHeight(30)),
            ElevatedButton(
              onPressed:
                  _isLoading || !_isEmailDomainValid || !_isPhoneValid
                      ? null
                      : _signUp,
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFF00ADB5),
                minimumSize: const Size(double.infinity, 55),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(6),
                ),
              ),
              child:
                  _isLoading
                      ? const CircularProgressIndicator(color: Colors.white)
                      : const Text(
                        'Create Account',
                        style: TextStyle(
                          color: Colors.white,
                          fontFamily: 'Montserrat',
                          fontWeight: FontWeight.w600,
                          fontSize: 18,
                        ),
                      ),
            ),
            SizedBox(height: SizeConfig.getPropHeight(30)),
            const Align(
              alignment: Alignment.center,
              child: Text(
                'Or Register with',
                style: TextStyle(
                  fontFamily: 'Montserrat',
                  fontWeight: FontWeight.w500,
                  fontSize: 12,
                  color: Color(0xFF303841),
                ),
              ),
            ),
            SizedBox(height: SizeConfig.getPropHeight(20)),
            SocialLoginButton(
              text: 'Connect with Google',
              iconPath: 'assets/icons/google.png',
              onPressed: () {},
            ),
            SizedBox(height: SizeConfig.getPropHeight(12)),
            SocialLoginButton(
              text: 'Connect With Facebook',
              iconPath: 'assets/icons/facebook.png',
              onPressed: () {},
              backgroundColor: const Color(0xFF1877F2),
              textColor: Colors.white,
            ),
            SizedBox(height: SizeConfig.getPropHeight(20)),
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Text(
                  "Already have an account? ",
                  style: TextStyle(
                    fontFamily: 'Montserrat',
                    fontWeight: FontWeight.w500,
                    fontSize: 14,
                  ),
                ),
                TextButton(
                  onPressed: () {
                    context.go('/login');
                  },
                  style: TextButton.styleFrom(
                    padding: EdgeInsets.zero,
                    minimumSize: Size.zero,
                    tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                  ),
                  child: const Text(
                    'Login!',
                    style: TextStyle(
                      color: Color(0xFF00ADB5),
                      fontFamily: 'Montserrat',
                      fontWeight: FontWeight.w600,
                      fontSize: 14,
                    ),
                  ),
                ),
              ],
            ),
            SizedBox(height: SizeConfig.getPropHeight(20)),
          ],
        ),
      ),
    );
  }
}
