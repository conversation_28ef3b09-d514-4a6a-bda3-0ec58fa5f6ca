import 'package:flutter_riverpod/flutter_riverpod.dart';

// 1. Data Model for Onboarding
// This class holds all the data we collect during the onboarding process.
class OnboardingData {
  final List<String> favoriteExercises;
  final String? gender;
  final int? age;
  final double? weight;
  final double? height;
  final String? fitnessLevel;
  final String? goal;

  OnboardingData({
    this.favoriteExercises = const [],
    this.gender,
    this.age,
    this.weight,
    this.height,
    this.fitnessLevel,
    this.goal,
  });

  // Method to create a copy with updated values
  OnboardingData copyWith({
    List<String>? favoriteExercises,
    String? gender,
    int? age,
    double? weight,
    double? height,
    String? fitnessLevel,
    String? goal,
  }) {
    return OnboardingData(
      favoriteExercises: favoriteExercises ?? this.favoriteExercises,
      gender: gender ?? this.gender,
      age: age ?? this.age,
      weight: weight ?? this.weight,
      height: height ?? this.height,
      fitnessLevel: fitnessLevel ?? this.fitnessLevel,
      goal: goal ?? this.goal,
    );
  }
}

// 2. StateNotifier for Onboarding Data
// This class manages the state of the OnboardingData.
class OnboardingNotifier extends StateNotifier<OnboardingData> {
  OnboardingNotifier() : super(OnboardingData());

  void updateFavoriteExercises(List<String> exercises) {
    state = state.copyWith(favoriteExercises: exercises);
  }

  void updateGender(String gender) {
    state = state.copyWith(gender: gender);
  }

  void updateAge(int age) {
    state = state.copyWith(age: age);
  }

  void updateWeight(double weight) {
    state = state.copyWith(weight: weight);
  }

  void updateHeight(double height) {
    state = state.copyWith(height: height);
  }

  void updateFitnessLevel(String level) {
    state = state.copyWith(fitnessLevel: level);
  }

  void updateGoal(String goal) {
    state = state.copyWith(goal: goal);
  }

  // Method to save the final data to Supabase would go here
  Future<void> saveOnboardingData() async {
    // TODO: Implement Supabase data saving logic in Task 5.9
    print("Saving data to Supabase...");
    print("Gender: ${state.gender}");
    print("Age: ${state.age}");
    // ... print other fields
  }
}

// 3. StateNotifierProvider
// This provider exposes the OnboardingNotifier to the rest of the app.
final onboardingProvider = StateNotifierProvider<OnboardingNotifier, OnboardingData>((ref) {
  return OnboardingNotifier();
}); 