import 'package:flutter/material.dart';

class PopularExerciseSection extends StatelessWidget {
  const PopularExerciseSection({super.key});

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            const Text(
              'Popular Exercise',
              style: TextStyle(
                fontFamily: 'Montserrat',
                fontSize: 16,
                fontWeight: FontWeight.w700,
                color: Color(0xFF191919),
              ),
            ),
            TextButton(
              onPressed: () {},
              style: TextButton.styleFrom(
                padding: EdgeInsets.zero,
                minimumSize: Size.zero,
                tapTargetSize: MaterialTapTargetSize.shrinkWrap,
              ),
              child: const Text(
                'See all',
                style: TextStyle(
                  fontFamily: 'Montserrat',
                  fontSize: 12,
                  fontWeight: FontWeight.w500,
                  color: Color(0xFF00ADB5),
                ),
              ),
            ),
          ],
        ),
        const SizedBox(height: 10),
        ExerciseCard(
          title: 'Full Shot Woman Stretching Arm',
          level: 'Beginner',
          duration: '30 min',
          imageUrl: 'https://images.unsplash.com/photo-1544367567-0f2fcb009e0b',
        ),
        const SizedBox(height: 10),
        const Divider(color: Color(0xFFE5E5E5), thickness: 1, height: 1),
        const SizedBox(height: 10),
        ExerciseCard(
          title: 'Athlete Practicing Monochrome',
          level: 'Beginner',
          duration: '50 min',
          imageUrl:
              'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b',
        ),
      ],
    );
  }
}

class ExerciseCard extends StatelessWidget {
  final String title;
  final String level;
  final String duration;
  final String imageUrl;

  const ExerciseCard({
    super.key,
    required this.title,
    required this.level,
    required this.duration,
    required this.imageUrl,
  });

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: 246,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            width: double.infinity,
            height: 180,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(15),
              image: DecorationImage(
                image: NetworkImage(imageUrl),
                fit: BoxFit.cover,
              ),
            ),
            child: Stack(
              children: [
                Positioned(
                  top: 10,
                  right: 10,
                  child: SizedBox(
                    width: 24,
                    height: 24,
                    child: const Icon(
                      Icons.favorite_border,
                      size: 24,
                      color: Colors.white,
                    ),
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: const TextStyle(
                    fontFamily: 'Montserrat',
                    fontSize: 12,
                    fontWeight: FontWeight.w600,
                    color: Color(0xFF191919),
                  ),
                ),
                const SizedBox(height: 8),
                Row(
                  children: [
                    Text(
                      level,
                      style: const TextStyle(
                        fontFamily: 'Montserrat',
                        fontSize: 10,
                        fontWeight: FontWeight.w400,
                        color: Color(0xFF303841),
                      ),
                    ),
                    const SizedBox(width: 8),
                    Container(
                      width: 1,
                      height: 12,
                      color: const Color(0xFF3A4750),
                    ),
                    const SizedBox(width: 8),
                    SizedBox(
                      width: 14,
                      height: 14,
                      child: const Icon(
                        Icons.access_time,
                        size: 14,
                        color: Color(0xFF00ADB5),
                      ),
                    ),
                    const SizedBox(width: 4),
                    Text(
                      duration,
                      style: const TextStyle(
                        fontFamily: 'Montserrat',
                        fontSize: 10,
                        fontWeight: FontWeight.w500,
                        color: Color(0xFF3A4750),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
