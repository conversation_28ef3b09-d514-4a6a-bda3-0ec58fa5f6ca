import 'package:flutter/material.dart';
import 'package:fluttertoast/fluttertoast.dart';

enum ToastType { success, error, warning }

void showAppToast({
  required BuildContext context,
  required String message,
  ToastType type = ToastType.error,
}) {
  Color backgroundColor;
  Color textColor = Colors.white;

  switch (type) {
    case ToastType.success:
      backgroundColor = Colors.green;
      break;
    case ToastType.error:
      backgroundColor = Colors.red;
      break;
    case ToastType.warning:
      backgroundColor = Colors.orange;
      break;
  }

  Fluttertoast.showToast(
    msg: message,
    toastLength: Toast.LENGTH_LONG,
    gravity: ToastGravity.TOP,
    backgroundColor: backgroundColor,
    textColor: textColor,
    fontSize: 16.0,
  );
}
