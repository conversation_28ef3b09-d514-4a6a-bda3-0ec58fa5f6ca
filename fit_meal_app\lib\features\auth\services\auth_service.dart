import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:flutter_libphonenumber/flutter_libphonenumber.dart';
import 'package:google_sign_in/google_sign_in.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:flutter/foundation.dart';

// This provider will be used to access the AuthService from the UI.
final authServiceProvider = Provider<AuthService>((ref) {
  return AuthService(Supabase.instance.client);
});

class AuthService {
  final GoTrueClient _auth;

  AuthService(SupabaseClient client) : _auth = client.auth;

  Future<void> signInWithPassword(String email, String password) async {
    try {
      await _auth.signInWithPassword(email: email, password: password);
    } on AuthException {
      // It's better to handle specific errors and show user-friendly messages.
      // For now, we'll just rethrow the exception.
      rethrow;
    } catch (e) {
      // Handle other potential errors
      rethrow;
    }
  }

  Future<void> signUpWithPassword(
    String email,
    String password, {
    String? fullName,
    String? phone,
    String? isoCode,
  }) async {
    // International phone number validation using the new package
    if (phone != null && phone.isNotEmpty && isoCode != null) {
      try {
        await parse(phone, region: isoCode);
      } catch (e) {
        throw const AuthException(
          'Please enter a valid phone number for the selected country.',
        );
      }
    }

    // Email domain validation
    final validDomains = [
      'gmail.com',
      'yahoo.com',
      'outlook.com',
      'hotmail.com',
      'icloud.com',
    ];
    final emailDomain = email.split('@').last;
    if (!validDomains.contains(emailDomain)) {
      throw const AuthException(
        'Please use a valid email provider (e.g., Gmail, Outlook).',
      );
    }

    // Password policy validation
    if (password.length < 8) {
      throw const AuthException('Password must be at least 8 characters long.');
    }
    if (!password.contains(RegExp(r'[A-Z]'))) {
      throw const AuthException(
        'Password must contain at least one uppercase letter.',
      );
    }
    if (!password.contains(RegExp(r'[a-z]'))) {
      throw const AuthException(
        'Password must contain at least one lowercase letter.',
      );
    }
    if (!password.contains(RegExp(r'[0-9]'))) {
      throw const AuthException('Password must contain at least one number.');
    }

    try {
      await _auth.signUp(
        email: email,
        password: password,
        data: {'full_name': fullName, 'phone': phone},
      );
    } on AuthException {
      rethrow;
    } catch (e) {
      rethrow;
    }
  }

  Future<void> sendPasswordResetEmail(String email) async {
    try {
      await _auth.resetPasswordForEmail(
        email,
        redirectTo: 'io.supabase.flutter://reset-password',
      );
    } on AuthException {
      rethrow;
    } catch (e) {
      rethrow;
    }
  }

  Future<void> updatePassword(String newPassword) async {
    try {
      await _auth.updateUser(UserAttributes(password: newPassword));
    } on AuthException {
      rethrow;
    } catch (e) {
      rethrow;
    }
  }

  Future<void> signInWithGoogle() async {
    try {
      // Get client IDs from environment
      final androidClientId = dotenv.env['GOOGLE_ANDROID_CLIENT_ID'] ?? '';
      final iosClientId = dotenv.env['GOOGLE_IOS_CLIENT_ID'] ?? '';
      final webClientId = dotenv.env['GOOGLE_WEB_CLIENT_ID'] ?? '';

      if (kDebugMode) {
        print('🔍 Google OAuth Debug:');
        print(
          'Android Client ID: ${androidClientId.isNotEmpty ? "✅ Found" : "❌ Missing"}',
        );
        print(
          'iOS Client ID: ${iosClientId.isNotEmpty ? "✅ Found" : "❌ Missing"}',
        );
        print(
          'Web Client ID: ${webClientId.isNotEmpty ? "✅ Found" : "❌ Missing"}',
        );
      }

      final GoogleSignIn googleSignIn = GoogleSignIn(
        clientId: iosClientId.isNotEmpty ? iosClientId : null,
        serverClientId: webClientId.isNotEmpty ? webClientId : null,
      );

      if (kDebugMode) {
        print('🚀 Starting Google Sign-In...');
      }

      // Trigger the authentication flow
      final GoogleSignInAccount? googleUser = await googleSignIn.signIn();

      if (googleUser == null) {
        if (kDebugMode) {
          print('❌ Google sign-in was cancelled by user');
        }
        throw const AuthException('Google sign-in was cancelled');
      }

      if (kDebugMode) {
        print('✅ Google user signed in: ${googleUser.email}');
      }

      // Obtain the auth details from the request
      final GoogleSignInAuthentication googleAuth =
          await googleUser.authentication;

      if (googleAuth.accessToken == null || googleAuth.idToken == null) {
        if (kDebugMode) {
          print('❌ Failed to get Google authentication tokens');
          print('Access Token: ${googleAuth.accessToken != null ? "✅" : "❌"}');
          print('ID Token: ${googleAuth.idToken != null ? "✅" : "❌"}');
        }
        throw const AuthException('Failed to get Google authentication tokens');
      }

      if (kDebugMode) {
        print('✅ Got Google authentication tokens');
        print('🔄 Signing in to Supabase...');
      }

      // Sign in to Supabase with Google credentials
      await _auth.signInWithIdToken(
        provider: OAuthProvider.google,
        idToken: googleAuth.idToken!,
        accessToken: googleAuth.accessToken!,
      );

      if (kDebugMode) {
        print('✅ Successfully signed in to Supabase with Google');
      }
    } on AuthException catch (e) {
      if (kDebugMode) {
        print('❌ AuthException: ${e.message}');
      }
      rethrow;
    } catch (e) {
      if (kDebugMode) {
        print('❌ Unexpected error: ${e.toString()}');
      }
      throw AuthException('Google sign-in failed: ${e.toString()}');
    }
  }

  Future<void> signOut() async {
    try {
      // Sign out from Google
      final GoogleSignIn googleSignIn = GoogleSignIn();
      await googleSignIn.signOut();

      // Sign out from Supabase
      await _auth.signOut();
    } on AuthException {
      rethrow;
    } catch (e) {
      rethrow;
    }
  }
}
