# Task ID: 1
# Title: Project Setup and CI/CD Pipeline
# Status: done
# Dependencies: None
# Priority: high
# Description: Initialize the Flutter project, configure dependencies, and set up a CI/CD pipeline using GitHub Actions for automated testing and builds.
# Details:
Create a new Flutter project using the latest stable version (e.g., Flutter 3.22.x). Add required dependencies to `pubspec.yaml`: `flutter_riverpod`, `supabase_flutter`, `go_router`, `hive`, `hive_flutter`, `firebase_core`, `firebase_messaging`, `revenuecat_purchases_flutter`, `cached_network_image`. Set up a GitHub Actions workflow (`.github/workflows/main.yml`) that triggers on push/pull_request to `main` branch. The workflow should run `flutter analyze`, `flutter test`, and `flutter build apk --release` and `flutter build ipa --release`.

# Test Strategy:
Verify that the CI pipeline successfully completes all steps: linting, running unit tests, and building both Android and iOS artifacts without errors.

# Subtasks:
## 1. Initialize Flutter Project and Add Dependencies [done]
### Dependencies: None
### Description: Create a new Flutter project and add all the required dependencies as specified in the project requirements.
### Details:
Use the `flutter create` command to initialize the project. Add all necessary packages to the `pubspec.yaml` file and run `flutter pub get` to install them.

## 2. Create Base GitHub Actions Workflow for Analysis and Testing [done]
### Dependencies: 1.1
### Description: Set up a basic GitHub Actions workflow file that triggers on push and pull requests. This workflow should include jobs for running `flutter analyze` and `flutter test`.
### Details:
Create a `.github/workflows/main.yml` file. Define triggers for `push` and `pull_request` events. Add two separate jobs: one for `flutter analyze` to check code quality and another for `flutter test` to run unit and widget tests.

## 3. Add Android Release Build Job to Workflow [done]
### Dependencies: 1.2
### Description: Extend the GitHub Actions workflow to include a job for building the Android release APK. This includes setting up the keystore using GitHub Secrets for signing the application.
### Details:
Add a new job to the `main.yml` workflow for building the Android release APK (`flutter build apk --release`). Configure GitHub Secrets for the keystore file (base64 encoded), keystore password, key alias, and key password. The workflow step should decode the keystore file and use the secrets to sign the APK.

## 4. Add iOS Release Build Job to Workflow [done]
### Dependencies: 1.2
### Description: Add a final job to the workflow for building the iOS release IPA. This involves configuring code signing with certificates and provisioning profiles stored securely in GitHub Secrets.
### Details:
Add another job to the `main.yml` workflow that runs on a macOS runner. This job will build the iOS release IPA (`flutter build ipa --release`). Configure GitHub Secrets for the distribution certificate (base64 encoded), certificate password, and the provisioning profile (base64 encoded). The workflow steps must install the certificate and provisioning profile before executing the build command.

