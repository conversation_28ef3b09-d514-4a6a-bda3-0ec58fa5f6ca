# Task ID: 5
# Title: New User Onboarding and Personalization Flow
# Status: pending
# Dependencies: 2, 3, 4
# Priority: high
# Description: Implement the multi-screen user onboarding flow to collect personalization data after the initial sign-up and save it to the user's profile in Supabase.
# Details:
This task involves creating a sequential, multi-step onboarding process that captures user details for personalization. The flow should be triggered immediately after a new user successfully signs up and verifies their account. The sequence of screens is: Favorites, Gender, Age, Weight, Height, Fitness Level, and Goal. Use a `PageView` or `go_router` to manage the navigation between these steps. State management for the collected data across screens should be handled using a dedicated `Riverpod` provider. Upon completion of the final step, the collected data (gender, age, weight, height, level, goal) should be saved to the corresponding columns in the `users` table in Supabase for the currently authenticated user. After a successful data save, the user should be navigated to the main application dashboard (Home screen).

# Test Strategy:
1. **Widget Testing**: Create widget tests for each individual onboarding screen to verify UI elements render correctly and state updates upon user interaction (e.g., selecting a gender, using a slider for age). 2. **Integration Testing**: Write an integration test for the complete onboarding flow, simulating user input on each screen and verifying the final data payload. 3. **Manual End-to-End Testing**: Perform a manual test starting from user sign-up. After successful login, verify that the onboarding flow starts automatically. Proceed through all screens, providing valid data. On the final step, confirm that the data is correctly updated in the Supabase `users` table for the test user's `user_id`. Also, verify that the user is redirected to the Home screen upon completion and that the onboarding flow does not trigger again on subsequent app launches.

# Subtasks:
## 1. Onboarding State Management & Navigation [pending]
### Dependencies: None
### Description: Create a Riverpod provider to manage onboarding data across screens and set up PageView for navigation.
### Details:


## 2. Screen 1: Select Favorite [pending]
### Dependencies: None
### Description: Implement the UI for the 'Select Favorite' screen (Figma: 09_Select Favorite).
### Details:


## 3. Screen 2: Gender [pending]
### Dependencies: None
### Description: Implement the UI for the 'Gender' selection screen (Figma: 10_Gender).
### Details:


## 4. Screen 3: Age [pending]
### Dependencies: None
### Description: Implement the UI for the 'Age' selection screen (Figma: 11_Age).
### Details:


## 5. Screen 4: Weight [pending]
### Dependencies: None
### Description: Implement the UI for the 'Weight' selection screen (Figma: 12_Weight).
### Details:


## 6. Screen 5: Height [pending]
### Dependencies: None
### Description: Implement the UI for the 'Height' selection screen (Figma: 14_Height).
### Details:


## 7. Screen 6: Fitness Level [pending]
### Dependencies: None
### Description: Implement the UI for the 'Fitness Level' selection screen (Figma: 15_Level).
### Details:


## 8. Screen 7: Goal [pending]
### Dependencies: None
### Description: Implement the UI for the 'Goal' selection screen (Figma: 16_Goal).
### Details:


## 9. Save & Finalize Onboarding [pending]
### Dependencies: None
### Description: Save all collected onboarding data to Supabase and navigate the user to the home screen upon completion (Figma: 17_Get Started).
### Details:


