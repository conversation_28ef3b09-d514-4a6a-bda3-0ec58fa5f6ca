import 'package:flutter/material.dart';

class MealsListSection extends StatefulWidget {
  final String selectedTab;

  const MealsListSection({super.key, required this.selectedTab});

  @override
  State<MealsListSection> createState() => _MealsListSectionState();
}

class _MealsListSectionState extends State<MealsListSection> {
  final Set<int> _favoriteMeals = {};

  void _toggleFavorite(int index) {
    setState(() {
      if (_favoriteMeals.contains(index)) {
        _favoriteMeals.remove(index);
      } else {
        _favoriteMeals.add(index);
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    final meals = _getMealsForTab(widget.selectedTab);

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // "15 meals" title
          const Text(
            '15 meals',
            style: TextStyle(
              fontFamily: 'Montserrat',
              fontWeight: FontWeight.w700,
              fontSize: 16,
              color: Color(0xFF303841),
              letterSpacing: 0,
              height: 1.22,
            ),
          ),

          const SizedBox(height: 16),

          // Meals list
          Expanded(
            child: ListView.builder(
              itemCount: meals.length,
              itemBuilder: (context, index) {
                final meal = meals[index];
                return Column(
                  children: [
                    MealCard(
                      title: meal['title'],
                      calories: meal['calories'],
                      imageUrl: meal['image'],
                      isFavorite: _favoriteMeals.contains(index),
                      onFavoriteToggle: () => _toggleFavorite(index),
                    ),
                    if (index < meals.length - 1)
                      const Column(
                        children: [
                          SizedBox(height: 10),
                          Divider(
                            color: Color(0xFFE5E5E5),
                            thickness: 1,
                            height: 1,
                          ),
                          SizedBox(height: 10),
                        ],
                      ),
                  ],
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  List<Map<String, dynamic>> _getMealsForTab(String tab) {
    // Breakfast meals as shown in Figma
    if (tab == 'Breakfast') {
      return [
        {
          'title': 'Green beans, tomatoes, eggs',
          'calories': '135 kcal',
          'image':
              'https://images.unsplash.com/photo-1482049016688-2d3e1b311543?w=400&h=300&fit=crop&crop=focalpoint&fp-x=0.5&fp-y=0.5',
        },
        {
          'title': 'Healthy balanced vegetarian food',
          'calories': '135 kcal',
          'image':
              'https://images.unsplash.com/photo-1512621776951-a57141f2eefd?w=400&h=300&fit=crop&crop=focalpoint&fp-x=0.5&fp-y=0.5',
        },
        {
          'title': 'Ketogenic/paleo diet. fried eggs, salmon',
          'calories': '135 kcal',
          'image':
              'https://images.unsplash.com/photo-1467003909585-2f8a72700288?w=400&h=300&fit=crop&crop=focalpoint&fp-x=0.5&fp-y=0.5',
        },
      ];
    }

    // Sample data for other tabs
    return [
      {
        'title': 'Sample $tab meal 1',
        'calories': '250 kcal',
        'image':
            'https://images.unsplash.com/photo-**********-c74683f339c1?w=400&h=300&fit=crop&crop=focalpoint&fp-x=0.5&fp-y=0.5',
      },
      {
        'title': 'Sample $tab meal 2',
        'calories': '300 kcal',
        'image':
            'https://images.unsplash.com/photo-1532550907401-a500c9a57435?w=400&h=300&fit=crop&crop=focalpoint&fp-x=0.5&fp-y=0.5',
      },
    ];
  }
}

class MealCard extends StatelessWidget {
  final String title;
  final String calories;
  final String imageUrl;
  final bool isFavorite;
  final VoidCallback onFavoriteToggle;

  const MealCard({
    super.key,
    required this.title,
    required this.calories,
    required this.imageUrl,
    required this.isFavorite,
    required this.onFavoriteToggle,
  });

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: 250,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            width: double.infinity,
            height: 180,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(15),
              image: DecorationImage(
                image: NetworkImage(imageUrl),
                fit: BoxFit.cover,
              ),
            ),
            child: Stack(
              children: [
                Positioned(
                  top: 10,
                  right: 10,
                  child: GestureDetector(
                    onTap: onFavoriteToggle,
                    child: Container(
                      width: 32,
                      height: 32,
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Icon(
                        isFavorite ? Icons.favorite : Icons.favorite_border,
                        size: 18,
                        color:
                            isFavorite
                                ? const Color(0xFF00ADB5)
                                : const Color(0xFF191919),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: const TextStyle(
                    fontFamily: 'Montserrat',
                    fontSize: 12,
                    fontWeight: FontWeight.w600,
                    color: Color(0xFF191919),
                  ),
                ),
                const SizedBox(height: 8),
                Row(
                  children: [
                    SizedBox(
                      width: 14,
                      height: 14,
                      child: const Icon(
                        Icons.local_fire_department,
                        size: 14,
                        color: Color(0xFF00ADB5),
                      ),
                    ),
                    const SizedBox(width: 4),
                    Text(
                      calories,
                      style: const TextStyle(
                        fontFamily: 'Montserrat',
                        fontSize: 10,
                        fontWeight: FontWeight.w500,
                        color: Color(0xFF3A4750),
                      ),
                    ),
                    const SizedBox(width: 8),
                    Container(
                      width: 1,
                      height: 12,
                      color: const Color(0xFF3A4750),
                    ),
                    const SizedBox(width: 8),
                    SizedBox(
                      width: 14,
                      height: 14,
                      child: const Icon(
                        Icons.access_time,
                        size: 14,
                        color: Color(0xFF00ADB5),
                      ),
                    ),
                    const SizedBox(width: 4),
                    const Text(
                      '30 min',
                      style: TextStyle(
                        fontFamily: 'Montserrat',
                        fontSize: 10,
                        fontWeight: FontWeight.w500,
                        color: Color(0xFF3A4750),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
