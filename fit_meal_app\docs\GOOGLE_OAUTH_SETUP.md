# Google OAuth Setup Guide for FitMeal App

This guide walks you through setting up Google OAuth authentication for the FitMeal Flutter app.

## Prerequisites

- Google Cloud Console account
- Flutter development environment
- Android Studio (for Android configuration)
- Xcode (for iOS configuration)

## Step 1: Google Cloud Console Setup

1. **Create a new project** (or select existing one):
   - Go to [Google Cloud Console](https://console.cloud.google.com/)
   - Create a new project named "FitMeal App"

2. **Enable Google+ API**:
   - Navigate to "APIs & Services" > "Library"
   - Search for "Google+ API" and enable it

3. **Configure OAuth consent screen**:
   - Go to "APIs & Services" > "OAuth consent screen"
   - Choose "External" user type
   - Fill in required information:
     - App name: "Fit<PERSON>eal"
     - User support email: your email
     - Developer contact information: your email

## Step 2: Create OAuth 2.0 Credentials

### For Android:

1. **Create Android OAuth client**:
   - Go to "APIs & Services" > "Credentials"
   - Click "Create Credentials" > "OAuth client ID"
   - Select "Android" as application type
   - Package name: `com.fitmeal.app`
   - SHA-1 certificate fingerprint: (see below how to get it)

2. **Get SHA-1 fingerprint**:
   ```bash
   # For debug keystore (development)
   keytool -list -v -keystore ~/.android/debug.keystore -alias androiddebugkey -storepass android -keypass android

   # For release keystore (production)
   keytool -list -v -keystore path/to/your/release.keystore -alias your_key_alias
   ```

3. **Download google-services.json**:
   - After creating the Android client, download `google-services.json`
   - Place it in `android/app/google-services.json`

### For iOS:

1. **Create iOS OAuth client**:
   - Click "Create Credentials" > "OAuth client ID"
   - Select "iOS" as application type
   - Bundle ID: `com.fitmeal.app`

2. **Download GoogleService-Info.plist**:
   - Download the configuration file
   - Place it in `ios/Runner/GoogleService-Info.plist`

3. **Update iOS URL scheme**:
   - Open `GoogleService-Info.plist`
   - Find the `REVERSED_CLIENT_ID` value
   - Replace `YOUR_REVERSED_CLIENT_ID` in `ios/Runner/Info.plist` with this value

### For Web (optional):

1. **Create Web OAuth client**:
   - Select "Web application" as application type
   - Add authorized origins: `http://localhost:3000` (for development)

## Step 3: Update Environment Variables

Update the `.env` file with your OAuth client IDs:

```env
# Replace with your actual client IDs from Google Cloud Console
GOOGLE_WEB_CLIENT_ID=your_web_client_id_here.apps.googleusercontent.com
GOOGLE_IOS_CLIENT_ID=your_ios_client_id_here.apps.googleusercontent.com
```

## Step 4: Configure Supabase

1. **Enable Google provider in Supabase**:
   - Go to your Supabase project dashboard
   - Navigate to "Authentication" > "Providers"
   - Enable "Google" provider
   - Add your Google OAuth client ID and secret

2. **Configure redirect URLs**:
   - Add your app's redirect URLs in Supabase:
     - `io.supabase.flutter://login-callback/` (for mobile)
     - `http://localhost:3000/auth/callback` (for web, if applicable)

## Step 5: Install Dependencies

Run the following command to install the new dependencies:

```bash
cd fit_meal_app
flutter pub get
```

## Step 6: Test the Implementation

1. **Run the app**:
   ```bash
   flutter run
   ```

2. **Test Google Sign-In**:
   - Navigate to the login screen
   - Tap "Connect with Google"
   - Complete the OAuth flow
   - Verify successful authentication

## Troubleshooting

### Common Issues:

1. **"Sign in failed" error**:
   - Verify SHA-1 fingerprint is correct
   - Check that `google-services.json` is in the correct location
   - Ensure Google+ API is enabled

2. **iOS build errors**:
   - Verify `GoogleService-Info.plist` is added to the Xcode project
   - Check that URL scheme matches the `REVERSED_CLIENT_ID`

3. **Environment variables not loading**:
   - Ensure `.env` file is in the root of `fit_meal_app/`
   - Verify `flutter_dotenv` is properly configured in `main.dart`

### Debug Commands:

```bash
# Check SHA-1 fingerprint
keytool -list -v -keystore ~/.android/debug.keystore -alias androiddebugkey -storepass android -keypass android

# Clean and rebuild
flutter clean
flutter pub get
flutter run
```

## Security Notes

- Never commit `google-services.json` or `GoogleService-Info.plist` to version control
- Keep your OAuth client secrets secure
- Use different OAuth clients for development and production
- Regularly rotate your OAuth credentials

## Next Steps

After successful setup:
1. Test the authentication flow thoroughly
2. Implement proper error handling for edge cases
3. Add sign-out functionality
4. Consider implementing additional OAuth providers (Facebook, Apple)
