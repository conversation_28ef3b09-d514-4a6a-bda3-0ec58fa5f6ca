# Task ID: 2
# Title: Backend Setup: Supabase Schema and RLS
# Status: done
# Dependencies: None
# Priority: high
# Description: Set up the Supabase project, define the PostgreSQL database schema, and configure Row Level Security (RLS) policies for data protection.
# Details:
Create a new project on Supabase. Use the SQL editor to run scripts creating the tables: `users`, `exercises`, `workout_programs`, `meals`, `meal_plans`, `progress` as defined in the PRD. Enable RLS on all tables containing user-specific data. For the `progress` table, the policy should be: `CREATE POLICY "Enable read access for own data" ON public.progress FOR SELECT USING (auth.uid() = user_id);` and a similar one for inserts. This ensures users can only access their own data.

# Test Strategy:
Write SQL tests in the Supabase dashboard to verify RLS policies. For example, try to `SELECT` or `INSERT` data for another user's `user_id` and confirm the operation is denied.

# Subtasks:
## 1. Create Database Tables [done]
### Dependencies: None
### Description: Write and execute SQL scripts to create the necessary tables: `users`, `exercises`, `workout_programs`, `meals`, `meal_plans`, and `progress`. Define all columns, data types, primary keys, foreign keys, and other constraints.
### Details:
The script should create all required tables (`users`, `exercises`, `workout_programs`, `meals`, `meal_plans`, `progress`) with appropriate columns, data types, and foreign key relationships to establish the database schema.

## 2. Implement Row-Level Security (RLS) Policies [done]
### Dependencies: 2.1
### Description: For each table, enable Row-Level Security and write specific SQL security policies to ensure users can only perform CRUD operations on their own data.
### Details:
Enable RLS on all tables created in the previous step. Write and apply policies for SELECT, INSERT, UPDATE, and DELETE operations. Policies should typically use a `USING` or `WITH CHECK` clause comparing a `user_id` column with `auth.uid()`.

## 3. Test RLS Policies [done]
### Dependencies: 2.2
### Description: Create and execute a suite of SQL tests within the Supabase dashboard to validate that all RLS policies are functioning correctly for all CRUD operations on every table.
### Details:
Write test queries to verify policies. For example: As User A, attempt to SELECT, INSERT, UPDATE, and DELETE data belonging to User B (all should fail). Confirm that all operations by User A on their own data succeed as expected.

