{"master": {"tasks": [{"id": 1, "title": "Project Setup and CI/CD Pipeline", "description": "Initialize the Flutter project, configure dependencies, and set up a CI/CD pipeline using GitHub Actions for automated testing and builds.", "details": "Create a new Flutter project using the latest stable version (e.g., Flutter 3.22.x). Add required dependencies to `pubspec.yaml`: `flutter_riverpod`, `supabase_flutter`, `go_router`, `hive`, `hive_flutter`, `firebase_core`, `firebase_messaging`, `revenuecat_purchases_flutter`, `cached_network_image`. Set up a GitHub Actions workflow (`.github/workflows/main.yml`) that triggers on push/pull_request to `main` branch. The workflow should run `flutter analyze`, `flutter test`, and `flutter build apk --release` and `flutter build ipa --release`.", "testStrategy": "Verify that the CI pipeline successfully completes all steps: linting, running unit tests, and building both Android and iOS artifacts without errors.", "priority": "high", "dependencies": [], "status": "done", "subtasks": [{"id": 1, "title": "Initialize Flutter Project and Add Dependencies", "description": "Create a new Flutter project and add all the required dependencies as specified in the project requirements.", "dependencies": [], "details": "Use the `flutter create` command to initialize the project. Add all necessary packages to the `pubspec.yaml` file and run `flutter pub get` to install them.", "status": "done", "testStrategy": ""}, {"id": 2, "title": "Create Base GitHub Actions Workflow for Analysis and Testing", "description": "Set up a basic GitHub Actions workflow file that triggers on push and pull requests. This workflow should include jobs for running `flutter analyze` and `flutter test`.", "dependencies": [1], "details": "Create a `.github/workflows/main.yml` file. Define triggers for `push` and `pull_request` events. Add two separate jobs: one for `flutter analyze` to check code quality and another for `flutter test` to run unit and widget tests.", "status": "done", "testStrategy": ""}, {"id": 3, "title": "Add Android Release Build Job to Workflow", "description": "Extend the GitHub Actions workflow to include a job for building the Android release APK. This includes setting up the keystore using GitHub Secrets for signing the application.", "dependencies": [2], "details": "Add a new job to the `main.yml` workflow for building the Android release APK (`flutter build apk --release`). Configure GitHub Secrets for the keystore file (base64 encoded), keystore password, key alias, and key password. The workflow step should decode the keystore file and use the secrets to sign the APK.", "status": "done", "testStrategy": ""}, {"id": 4, "title": "Add iOS Release Build Job to Workflow", "description": "Add a final job to the workflow for building the iOS release IPA. This involves configuring code signing with certificates and provisioning profiles stored securely in GitHub Secrets.", "dependencies": [2], "details": "Add another job to the `main.yml` workflow that runs on a macOS runner. This job will build the iOS release IPA (`flutter build ipa --release`). Configure GitHub Secrets for the distribution certificate (base64 encoded), certificate password, and the provisioning profile (base64 encoded). The workflow steps must install the certificate and provisioning profile before executing the build command.", "status": "done", "testStrategy": ""}]}, {"id": 2, "title": "Backend Setup: Supabase Schema and RLS", "description": "Set up the Supabase project, define the PostgreSQL database schema, and configure Row Level Security (RLS) policies for data protection.", "details": "Create a new project on Supabase. Use the SQL editor to run scripts creating the tables: `users`, `exercises`, `workout_programs`, `meals`, `meal_plans`, `progress` as defined in the PRD. Enable RLS on all tables containing user-specific data. For the `progress` table, the policy should be: `CREATE POLICY \"Enable read access for own data\" ON public.progress FOR SELECT USING (auth.uid() = user_id);` and a similar one for inserts. This ensures users can only access their own data.", "testStrategy": "Write SQL tests in the Supabase dashboard to verify RLS policies. For example, try to `SELECT` or `INSERT` data for another user's `user_id` and confirm the operation is denied.", "priority": "high", "dependencies": [], "status": "done", "subtasks": [{"id": 1, "title": "Create Database Tables", "description": "Write and execute SQL scripts to create the necessary tables: `users`, `exercises`, `workout_programs`, `meals`, `meal_plans`, and `progress`. Define all columns, data types, primary keys, foreign keys, and other constraints.", "dependencies": [], "details": "The script should create all required tables (`users`, `exercises`, `workout_programs`, `meals`, `meal_plans`, `progress`) with appropriate columns, data types, and foreign key relationships to establish the database schema.", "status": "done", "testStrategy": ""}, {"id": 2, "title": "Implement Row-Level Security (RLS) Policies", "description": "For each table, enable Row-Level Security and write specific SQL security policies to ensure users can only perform CRUD operations on their own data.", "dependencies": [1], "details": "Enable RLS on all tables created in the previous step. Write and apply policies for SELECT, INSERT, UPDATE, and DELETE operations. Policies should typically use a `USING` or `WITH CHECK` clause comparing a `user_id` column with `auth.uid()`.", "status": "done", "testStrategy": ""}, {"id": 3, "title": "Test RLS Policies", "description": "Create and execute a suite of SQL tests within the Supabase dashboard to validate that all RLS policies are functioning correctly for all CRUD operations on every table.", "dependencies": [2], "details": "Write test queries to verify policies. For example: As User A, attempt to SELECT, INSERT, UPDATE, and DELETE data belonging to User B (all should fail). Confirm that all operations by User A on their own data succeed as expected.", "status": "done", "testStrategy": ""}]}, {"id": 3, "title": "Core UI: Navigation, Theming, and Design System", "description": "Implement the core application shell, including bottom tab navigation and theming, based on the official Figma designs. This involves setting up navigation, defining light and dark themes with the specified color palette and typography, and creating initial design system components.", "status": "done", "dependencies": [1], "priority": "high", "details": "The primary source of truth for all UI is the Figma document (channel: cx4zzwdw, 43 screens). Use `go_router` to set up a `ShellRoute` for the main app sections: Home, Meal Plans, Exercise, and Profile. Define `ThemeData` for both light and dark modes using the specified color palette (Primary: #00adb5, Text: #191919, Secondary: #3a4750, Background: #fcfcfc) and the 'Montserrat' font family. The recommended approach is to extract Figma components and styles, converting them into reusable Flutter widgets. Ensure all components are theme-aware.", "testStrategy": "Widget tests to verify navigation between tabs (Home, Meal Plans, Exercise, Profile). Manual testing to switch between light and dark modes on all screens, comparing against the Figma designs for visual consistency. Check for correct font usage and color application.", "subtasks": [{"id": 1, "title": "Implement Home Screen with modular widgets", "description": "Refactored the Home Screen into 8 modular widgets based on Figma design, improving maintainability.", "details": "", "status": "done", "dependencies": [], "parentTaskId": 3}, {"id": 2, "title": "Implement Exercise Screen with modular widgets", "description": "Refactored the Exercise Screen into 3 modular widgets and aligned it with the Figma design.", "details": "", "status": "done", "dependencies": [], "parentTaskId": 3}, {"id": 3, "title": "Implement Profile Screen with modular widgets", "description": "Refactored the Profile Screen into 4 modular widgets based on Figma design.", "details": "", "status": "done", "dependencies": [], "parentTaskId": 3}, {"id": 4, "title": "Implement Meal Plans Screen with modular widgets", "description": "Refactored the Meal Plans Screen into 4 modular widgets, aligned with Figma, and added stateful favorite functionality.", "details": "", "status": "done", "dependencies": [], "parentTaskId": 3}]}, {"id": 4, "title": "User Authentication Flow", "description": "Implement the complete user authentication flow, including Login, Signup, Forgot Password, and Account Verification screens, and integrate them with Supabase Auth.", "details": "Create four separate screens for the authentication flow based on the Figma designs. Use the `supabase_flutter` package for all authentication logic: `supabase.auth.signUp()` for new user registration (which will trigger a verification email), `supabase.auth.signInWithPassword()` for logging in existing users, and `supabase.auth.resetPasswordForEmail()` for the forgot password functionality. Manage authentication state (e.g., loading, error, authenticated user) using `flutter_riverpod`. Configure `go_router` to handle navigation between these auth screens and redirect to the main application shell upon successful authentication. Ensure robust error handling is implemented to provide clear feedback to the user for scenarios like incorrect credentials, email already in use, or network issues.", "testStrategy": "Perform widget testing on each form to validate input fields (e.g., email format, password strength). Manually test the end-to-end user flows: 1) Sign up with a new email and verify the account via the confirmation link. 2) Log out and log back in with the new credentials. 3) Attempt to log in with incorrect credentials and verify the error message. 4) Use the 'Forgot Password' feature and complete the password reset process. All screens must be visually verified against the Figma designs in both light and dark modes.", "status": "done", "dependencies": [1, 2, 3], "priority": "high", "subtasks": [{"id": 1, "title": "Implement Email/Password Authentication", "description": "Implement the UI and logic for user sign-up, sign-in, and sign-out functionalities using the `supabase_flutter` package for email and password authentication.", "dependencies": [], "details": "This includes creating the necessary forms based on Figma screens, handling user input validation, and communicating with the Supabase backend to create and authenticate users.", "status": "done", "testStrategy": ""}, {"id": 2, "title": "Implement Google OAuth Sign-In", "description": "Configure and implement the Google OAuth sign-in flow. This involves setting up the project in the Google Cloud Console, configuring credentials, and integrating with `supabase_flutter`.", "dependencies": [], "details": "Requires platform-specific setup for Android (e.g., `google-services.json`, SHA-1 fingerprint) and iOS (e.g., URL schemes, `GoogleService-Info.plist`).\n<info added on 2025-07-03T06:06:50.298Z>\nCOMPLETED IMPLEMENTATION:\nAdded google_sign_in dependency (^6.2.1) to pubspec.yaml\nImplemented signInWithGoogle() method in AuthService using Supabase OAuth flow\nAdded signOut() method with Google Sign-In cleanup\nUpdated Android build.gradle.kts with Google Services plugin\nConfigured iOS Info.plist with URL schemes for Google OAuth\nAdded environment variables for Google OAuth client IDs\nCreated comprehensive setup documentation (GOOGLE_OAUTH_SETUP.md)\n\nTECHNICAL DETAILS:\n- AuthService now supports Google OAuth via Supabase's signInWithIdToken()\n- Proper error handling for cancelled sign-in and missing tokens\n- Environment-based configuration for client IDs (web and iOS)\n- Platform-specific setup for both Android and iOS\n- Integration with existing UI (login screen already calls the method)\n\nNEXT STEPS REQUIRED:\n1. Set up Google Cloud Console project and OAuth credentials\n2. Download and place google-services.json (Android) and GoogleService-Info.plist (iOS)\n3. Update .env file with actual client IDs\n4. Configure Supabase Google provider\n5. Test the complete OAuth flow\n\nThe implementation is code-complete and ready for configuration and testing.\n</info added on 2025-07-03T06:06:50.298Z>", "status": "done", "testStrategy": ""}, {"id": 3, "title": "Implement Apple OAuth Sign-In", "description": "Configure and implement the Apple OAuth sign-in flow. This requires setting up an App ID and Service ID in the Apple Developer portal and integrating with `supabase_flutter`.", "dependencies": [], "details": "This is an iOS-specific feature and requires configuration within Xcode (e.g., adding the 'Sign in with Apple' capability) and the Apple Developer account.", "status": "done", "testStrategy": ""}, {"id": 4, "title": "Implement Auth State Management and Routing", "description": "Create a Riverpod provider to manage the global authentication state by listening to the `onAuthStateChange` stream from Supabase. Configure `go_router` to automatically redirect users based on this state.", "dependencies": [1, 2], "details": "This involves setting up a `StreamProvider` in Riverpod and using the `redirect` functionality within the `GoRouter` configuration to protect routes and manage user navigation flow.", "status": "done", "testStrategy": ""}]}, {"id": 5, "title": "New User Onboarding and Personalization Flow", "description": "Implement the multi-screen user onboarding flow to collect personalization data after the initial sign-up and save it to the user's profile in Supabase.", "details": "This task involves creating a sequential, multi-step onboarding process that captures user details for personalization. The flow should be triggered immediately after a new user successfully signs up and verifies their account. The sequence of screens is: Favorites, Gender, Age, Weight, Height, Fitness Level, and Goal. Use a `PageView` or `go_router` to manage the navigation between these steps. State management for the collected data across screens should be handled using a dedicated `Riverpod` provider. Upon completion of the final step, the collected data (gender, age, weight, height, level, goal) should be saved to the corresponding columns in the `users` table in Supabase for the currently authenticated user. After a successful data save, the user should be navigated to the main application dashboard (Home screen).", "testStrategy": "1. **Widget Testing**: Create widget tests for each individual onboarding screen to verify UI elements render correctly and state updates upon user interaction (e.g., selecting a gender, using a slider for age). 2. **Integration Testing**: Write an integration test for the complete onboarding flow, simulating user input on each screen and verifying the final data payload. 3. **Manual End-to-End Testing**: Perform a manual test starting from user sign-up. After successful login, verify that the onboarding flow starts automatically. Proceed through all screens, providing valid data. On the final step, confirm that the data is correctly updated in the Supabase `users` table for the test user's `user_id`. Also, verify that the user is redirected to the Home screen upon completion and that the onboarding flow does not trigger again on subsequent app launches.", "status": "in-progress", "dependencies": [2, 3, 4], "priority": "high", "subtasks": [{"id": 1, "title": "Onboarding State Management & Navigation", "description": "Create a Riverpod provider to manage onboarding data across screens and set up PageView for navigation.", "details": "", "status": "done", "dependencies": [], "parentTaskId": 5}, {"id": 2, "title": "Screen 1: Select Favorite", "description": "Implement the UI for the 'Select Favorite' screen (Figma: 09_Select Favorite).", "details": "", "status": "done", "dependencies": [], "parentTaskId": 5}, {"id": 3, "title": "Screen 2: Gender", "description": "Implement the UI for the 'Gender' selection screen (Figma: 10_Gender).", "details": "", "status": "in-progress", "dependencies": [], "parentTaskId": 5}, {"id": 4, "title": "Screen 3: Age", "description": "Implement the UI for the 'Age' selection screen (Figma: 11_Age).", "details": "", "status": "pending", "dependencies": [], "parentTaskId": 5}, {"id": 5, "title": "Screen 4: Weight", "description": "Implement the UI for the 'Weight' selection screen (Figma: 12_Weight).", "details": "", "status": "pending", "dependencies": [], "parentTaskId": 5}, {"id": 6, "title": "Screen 5: Height", "description": "Implement the UI for the 'Height' selection screen (Figma: 14_Height).", "details": "", "status": "pending", "dependencies": [], "parentTaskId": 5}, {"id": 7, "title": "Screen 6: Fitness Level", "description": "Implement the UI for the 'Fitness Level' selection screen (Figma: 15_Level).", "details": "", "status": "pending", "dependencies": [], "parentTaskId": 5}, {"id": 8, "title": "Screen 7: Goal", "description": "Implement the UI for the 'Goal' selection screen (Figma: 16_Goal).", "details": "", "status": "pending", "dependencies": [], "parentTaskId": 5}, {"id": 9, "title": "Save & Finalize Onboarding", "description": "Save all collected onboarding data to Supabase and navigate the user to the home screen upon completion (Figma: 17_Get Started).", "details": "", "status": "pending", "dependencies": [], "parentTaskId": 5}]}, {"id": 6, "title": "Workout Core: Exercise Details, Live Training, and Results Screens", "description": "Develop the core workout loop, including screens for viewing exercise details, an active training session with timers, and a results summary screen that saves progress to Supabase.", "details": "This task involves creating the three key screens for the workout experience. First, the 'Exercise Details' screen will fetch and display information for a specific exercise from the Supabase `exercises` table, including instructions and a visual guide (GIF/video). Second, the 'Live Training' screen will manage the active workout session. It will use a state management solution like Riverpod to track the current exercise, sets, reps, and control work/rest timers. Navigation controls (next/previous exercise) should be included. Third, upon completion, the 'Results' screen will summarize the session (duration, total volume, etc.) and persist this data by creating a new entry in the `progress` table in Supabase, associated with the authenticated user's ID.", "testStrategy": "1. **Widget Tests**: Create widget tests for the 'Exercise Details' screen with mock data. Test the timer logic on the 'Live Training' screen independently. Verify the 'Results' screen correctly displays summary data passed to it. 2. **Integration Test**: Write a test that simulates a full workout flow: starting a workout, proceeding through at least two exercises and a rest period, finishing the workout, and verifying that the app navigates to the Results screen. 3. **Manual E2E Test**: Log in as a test user, select and start a workout. Complete the session and confirm the summary on the 'Results' screen is accurate. Finally, query the Supabase `progress` table directly to ensure a new record was successfully created with the correct `user_id` and session data.", "status": "pending", "dependencies": [2, 3, 4, 5], "priority": "high", "subtasks": []}, {"id": 7, "title": "Implement Meal Plan Details Screen", "description": "Implement the user interface for the Meal Plan Details screen, which will display a comprehensive breakdown of a selected meal plan, including daily meals and nutritional information.", "details": "This task involves creating a new screen that fetches and displays the details of a specific meal plan from Supabase. The screen should be accessible via `go_router` and accept a `meal_plan_id` as a route parameter. Use a `FutureProvider` from `flutter_riverpod` to asynchronously fetch the meal plan data, including all associated meals for each day of the week, from the `meal_plans` and `meals` tables. The UI, based on the Figma design, should present the information in a clear, organized manner, likely using a combination of `ListView`, `ExpansionTile` for each day, and custom widgets for individual meal cards (e.g., Breakfast, Lunch, Dinner). Each meal card should display its name, ingredients, and key nutritional information like calories, protein, carbs, and fat.", "testStrategy": "1. **Widget Testing**: Create widget tests for the Meal Plan Details screen using mock data. Verify that the screen correctly displays loading and error states. Test that when data is successfully provided, the meal plan title, daily sections, and individual meal cards are rendered correctly with all expected nutritional information. 2. **Integration Testing**: Write an integration test that simulates navigating from a meal plan list to the details screen. This test should use a real Supabase backend (or a mocked Supabase client) to confirm that the correct data is fetched and displayed for a given `meal_plan_id`. 3. **Manual Testing**: Manually navigate to the screen on a device/emulator. Verify the layout matches the Figma design across different screen sizes. Check for visual consistency with the application's theme (light and dark modes). Ensure scrolling is smooth and all interactive elements are responsive.", "status": "pending", "dependencies": [2, 3, 4], "priority": "medium", "subtasks": []}, {"id": 8, "title": "Kullanıcı Gelişim Paneli (Dashboard & Progress)", "description": "Create a unified dashboard screen that visualizes the user's fitness journey. This screen will display key metrics, progress charts over time, and summaries of completed workouts.", "details": "This task involves creating a new screen, likely serving as the 'Home' tab's main view, to act as the user's progress dashboard. Use a `StreamProvider` from `flutter_riverpod` to fetch and listen for real-time updates from the `progress` table in Supabase for the currently authenticated user (`auth.uid()`). The screen should also fetch the user's initial metrics (e.g., starting weight) collected during the onboarding flow (Task 5). Implement UI components based on Figma designs, including: 1) Key Metric Cards for displaying current weight, total workouts, and other summary stats. 2) Progress Charts, using a library like `fl_chart`, to visualize metrics like weight change over time. 3) A 'Recent Activity' list showing the last few completed workouts, linking to their respective results screens (from Task 6). The logic should handle calculations for derived data, such as total change in weight.", "testStrategy": "1. **Widget Testing**: Create widget tests for the dashboard screen using a mock Riverpod provider. Test the loading, error, and empty states (for new users with no data). Provide mock progress data and verify that metric cards and charts render the correct values. 2. **Integration Testing**: Write an integration test that simulates a user logging in, completing a workout (simulating the outcome of Task 6), and then navigating to the dashboard to confirm that the new progress data is displayed correctly and updates the charts. 3. **Manual Testing**: Manually complete several different workouts and verify the dashboard updates in real-time. Check that all calculations are correct and that the UI is visually consistent with the Figma designs in both light and dark modes.", "status": "pending", "dependencies": [2, 3, 4, 5, 6], "priority": "medium", "subtasks": []}, {"id": 9, "title": "Premium Membership and Purchase Flow", "description": "Implement the 'Unlock Premium' and 'Subscription Plan' screens, integrating with RevenueCat to handle in-app purchases and manage user subscription status.", "details": "This task involves creating the complete premium subscription flow. First, initialize the RevenueCat SDK using the API keys provided in the environment configuration. Create two new screens navigable via `go_router`: an 'Unlock Premium' screen that acts as a paywall or promotional page, and a 'Subscription Plans' screen. The 'Subscription Plans' screen will use `Purchases.getOfferings()` to fetch available subscription packages from RevenueCat and display them to the user with details like price and duration. On user selection, call `Purchases.purchasePackage()` to initiate the platform-native purchase flow. Implement a listener using `Purchases.addCustomerInfoUpdateListener` to react to changes in the user's subscription status. Upon successful purchase confirmation from the listener, update the user's profile in the Supabase `users` table (e.g., set an `is_premium` boolean to true) to unlock features app-wide. Also, implement a 'Restore Purchases' button that calls `Purchases.restorePurchases()`.", "testStrategy": "1. **Widget Testing**: Use a mocking framework (like `mockito`) to create a mock `Purchases` class. Write widget tests for the 'Subscription Plans' screen to verify that it correctly handles and displays loading, error, and success states when fetching mock offerings. Simulate a tap on a purchase button and verify that the corresponding mock purchase method is called. 2. **Integration/Manual Testing**: Configure sandbox tester accounts in App Store Connect and Google Play Console. Run the app on a physical device or simulator logged into a sandbox account. Perform an end-to-end test: log in as a user, navigate to the subscription screen, and complete a purchase. Verify that the RevenueCat `CustomerInfo` listener receives an update with an active entitlement. Check the Supabase database to confirm the user's `is_premium` status is updated correctly. Test the 'Restore Purchases' functionality to ensure it correctly syncs the subscription status.", "status": "pending", "dependencies": [1, 2, 3, 4], "priority": "high", "subtasks": []}, {"id": 10, "title": "Implement My Favorites Feature", "description": "Implement the 'My Favorites' screen, allowing authenticated users to save, view, and manage their favorite workouts and meals for quick access.", "details": "This task involves creating a new `user_favorites` table in Supabase with columns for `user_id`, `item_id`, and `item_type` ('workout' or 'meal'), along with appropriate RLS policies. A new 'My Favorites' screen, accessible from the user profile, will be built using `go_router`. This screen will feature two tabs to separately list favorited workouts and meals, fetched using a Riverpod provider that queries the new table. Additionally, a 'favorite' toggle button (e.g., a star icon) must be added to the 'Exercise Details' (Task 6) and 'Meal Plan Details' (Task 7) screens. Toggling this button will execute Supabase functions to add or remove the respective item from the `user_favorites` table for the current user.", "testStrategy": "1. **Backend**: Add a SQL test to verify the RLS policy on the new `user_favorites` table, ensuring users can only access their own entries. 2. **Widget Testing**: Create widget tests for the 'My Favorites' screen using mock data to verify the correct rendering of tabs, lists, loading states, and empty states. 3. **Integration Testing**: Perform an end-to-end test flow: a) Log in as a user. b) Navigate to a meal detail screen, mark it as a favorite, and verify it appears in the 'Meals' tab of the 'My Favorites' screen. c) Navigate to an exercise detail screen, favorite it, and verify it appears in the 'Workouts' tab. d) Test the unfavorite functionality from both the detail screens and the favorites list. e) Confirm that tapping an item in the favorites list navigates to the correct detail page.", "status": "pending", "dependencies": [2, 3, 4, 6, 7], "priority": "medium", "subtasks": []}, {"id": 11, "title": "Zamanlama ve <PERSON><PERSON> (Schedule)", "description": "Implement the Schedule screen, allowing users to view a calendar and plan their workouts and meals for specific dates.", "details": "This task involves creating a new 'Schedule' screen accessible via the main navigation. A new Supabase table, `user_schedule`, will be created with columns for `user_id`, `item_id`, `item_type` ('workout' or 'meal'), and `scheduled_date`, with appropriate RLS policies to ensure users can only access their own schedule. The UI will feature a calendar view (using a library like `table_calendar`) to select dates. A Riverpod provider will fetch scheduled items for the selected date from the new table. Users should be able to add workouts or meals to the schedule, which will likely involve navigating to the respective lists (from Task 6 & 7), selecting an item, and assigning it to a date.", "testStrategy": "1. **Backend**: Write a SQL test in Supabase to verify the RLS policy on the `user_schedule` table, ensuring users cannot read or write schedule entries for other users. 2. **Widget Testing**: Create widget tests for the Schedule screen using a mock Riverpod provider. Verify that the calendar renders correctly, events are displayed for the selected day, and the UI correctly handles loading, error, and empty states. 3. **Integration Testing**: Write a test to simulate the full flow: a user navigates to the schedule, selects a future date, adds a workout, and verifies that the workout now appears on the calendar for that date.", "status": "pending", "dependencies": [2, 3, 4, 6, 7], "priority": "low", "subtasks": []}, {"id": 12, "title": "Bildirimler ve Hatırlatıcılar (Notifications & Reminders)", "description": "Implement the user interface for viewing notifications and setting reminders. Integrate Firebase Cloud Messaging (FCM) to enable push notifications for scheduled events and other alerts.", "details": "This task involves a full-stack implementation of notifications. First, configure the `firebase_core` and `firebase_messaging` packages for both Android and iOS. Implement logic to request notification permissions from the user and retrieve the device's FCM token upon login. This token must be stored in a new Supabase table (e.g., `user_devices`) linked to the `user_id`. Create a new 'Notifications' screen to list historical alerts fetched from a `notifications` table. Also, build a 'Notification Settings' screen allowing users to toggle preferences. The core integration is with the Schedule feature (Task 11); a Supabase Edge Function should be created to read the `user_schedule` table, identify upcoming events, and trigger push notifications to the relevant user devices via the FCM API at the scheduled time.", "testStrategy": "1. **Backend**: Create a Supabase test to verify that the FCM token is correctly associated with a `user_id`. Manually trigger the Edge Function to ensure it queries the schedule and attempts to send a notification. 2. **Widget Testing**: Create widget tests for the 'Notifications' list screen using mock data. Test the 'Notification Settings' screen to ensure state changes are handled correctly. 3. **Integration Testing**: Use the Firebase Console to send a test message to a specific device token and confirm receipt in foreground, background, and terminated app states. 4. **E2E Test**: Log in as a user, schedule a workout for 5 minutes in the future, and close the app. Verify that a system notification is received at the correct time. Tapping the notification should open the app.", "status": "pending", "dependencies": [1, 2, 3, 4, 11], "priority": "low", "subtasks": []}, {"id": 13, "title": "<PERSON><PERSON><PERSON> ve Gelişmiş Profil <PERSON>", "description": "Implement the App Settings and Edit Profile screens, allowing users to manage application preferences like theme and notifications, and to update their personal profile information.", "details": "Create two new screens accessible from the main Profile tab. 1. **Edit Profile Screen**: This screen will allow authenticated users to modify their personal data. It should fetch the current user's profile data (name, age, weight, height, fitness level, goal) from the Supabase `profiles` table, which was initially populated during onboarding (Task 5). Use a `Form` widget with appropriate input fields. Upon saving, use a Riverpod provider to call `supabase.from('profiles').update({...})` with the new data, targeting the current user's ID. 2. **Settings Screen**: This screen will serve as a hub for application-level configurations. It should include: a toggle/selector for theme (Light/Dark/System) that updates the app's `ThemeData` via a provider; a navigation link to 'Notification Preferences' (from Task 12); a navigation link to 'Manage Subscription' (from Task 9); and a 'Logout' button that calls `supabase.auth.signOut()` and navigates the user back to the login screen (Task 4).", "testStrategy": "1. **Widget Testing**: Create widget tests for the 'Edit Profile' screen using a mock Riverpod provider to pre-fill the form with user data. Verify that input changes are captured and that the `update` method is called with the correct payload on save. For the 'Settings' screen, test that tapping the theme selector updates the theme state and that the 'Logout' button triggers the sign-out function. Use mock navigation to verify that links to 'Notifications' and 'Subscription' work correctly. 2. **Integration Testing**: Manually test the end-to-end flow. Navigate to 'Edit Profile', change a value like weight, save it, then navigate to the Dashboard (Task 8) to confirm the updated value is displayed. Test the 'Logout' button to ensure it redirects to the login screen. Verify that changing the theme in settings applies the new theme across the entire application.", "status": "pending", "dependencies": [2, 3, 4, 5, 9, 12], "priority": "low", "subtasks": []}, {"id": 14, "title": "Implement Search and Filtering Functionality", "description": "Implement a dedicated Filters screen and integrate a global search functionality to allow users to easily find workouts and meals based on various criteria.", "details": "This task involves creating a comprehensive search and filtering system. A reusable search bar widget should be added to the AppBar of main list screens (e.g., Workouts, Meals). A new 'Filters' screen, likely presented as a modal bottom sheet, will be created to house various filtering options such as categories, difficulty level, duration, and a toggle for 'My Favorites'. A dedicated Riverpod provider (`filterProvider`) will manage the state of the search query and selected filters. Data-fetching providers (e.g., for workouts and meals) will watch this `filterProvider` and dynamically adjust their Supabase queries. The Supabase queries should use `.ilike()` for case-insensitive text search on relevant fields (e.g., name, description) and `.eq()` or `.in()` for filter criteria. To filter by favorites, the query will need to perform a JOIN with the `user_favorites` table created in Task 10.", "testStrategy": "1. **Widget Testing**: Create widget tests for the new 'Filters' screen using a mock Riverpod provider. Verify that interacting with UI controls (e.g., checkboxes, sliders, toggles) correctly updates the state in the mock provider. Test the 'Apply' and 'Reset' filter buttons. 2. **Integration Testing**: Write an integration test for the search and filter flow. Simulate a user typing in the search bar and verify that the list of items (using mock data) updates accordingly. Then, open the filter screen, apply a filter, and verify that the list is further refined to match both the search query and the filter criteria. 3. **Manual Testing**: Manually verify the end-to-end functionality against a staging Supabase instance. Test various combinations of search terms and filters. Confirm that the 'My Favorites' filter correctly shows only items favorited by the logged-in user. Ensure clearing filters and search terms restores the original list.", "status": "pending", "dependencies": [1, 3, 4, 5, 7, 10], "priority": "medium", "subtasks": []}, {"id": 15, "title": "Çevrimdışı Kullanım (Caching)", "description": "Implement a local caching layer using Hive to store essential application data, such as workout and meal details, ensuring users can access this information while offline.", "details": "This task involves integrating Hive for local data persistence. First, ensure Hive is initialized in the `main()` function using `await Hive.initFlutter()`. Modify the data models for workouts and meals to support Hive by adding `@HiveType` annotations to the classes and `@HiveField` to their properties. Generate the necessary TypeAdapters by running `flutter pub run build_runner build`. The core of this task is to update the data fetching logic within the Riverpod providers (or repositories). The new strategy should be 'cache-then-network': first, attempt to display data from the local Hive box for instant UI loading. Concurrently, trigger a fetch from the Supabase API. If the API call is successful, update the Hive box with the fresh data and refresh the UI. If the API call fails (e.g., no network), the app will continue to function with the stale data from the cache. Open Hive boxes for each data type, such as `Hive.openBox<Workout>('workouts')` and `Hive.openBox<Meal>('meals')`, to store the respective data.", "testStrategy": "1. **Unit/Widget Testing**: Mock the Hive `Box` interface. Create tests for the repository/provider layer to verify the caching logic. Test the 'offline' scenario by mocking a network error from the Supabase client and asserting that the provider correctly falls back to returning data from the mock Hive box. Test the 'online' scenario by mocking a successful API response and verifying that the provider not only returns the new data but also calls the `put` or `putAll` method on the mock Hive box. 2. **Manual Integration Testing**: Run the app with an active internet connection. Navigate to the workout and meal list/detail screens to ensure the cache is populated. Then, disable all network connectivity (Wi-Fi and mobile data) on the test device. Relaunch the application and navigate to the same screens. Verify that the workout and meal data is still displayed correctly from the cache and that the app remains usable.", "status": "pending", "dependencies": [1, 2, 3, 6, 7], "priority": "low", "subtasks": []}, {"id": 16, "title": "Post-Development Optimization and Refactoring", "description": "Profile the application to identify and resolve performance bottlenecks, refactor the codebase for improved modularity and maintainability, and perform a final cleanup of unused assets and code.", "details": "This task focuses on improving the overall quality and performance of the application after core features are implemented. 1. **Performance Profiling**: Use Flutter DevTools to analyze the app's performance. Focus on the Performance view to identify sources of jank and dropped frames. Use the CPU Profiler to find long-running methods and the Memory view to detect leaks. Address common issues like unnecessary widget rebuilds (by using `const` constructors and selective providers), optimizing list views (`ListView.builder`), and reducing layout complexity. 2. **Code Refactoring**: Review the entire codebase for maintainability. Break down large, monolithic widgets into smaller, reusable components. Ensure business logic is properly encapsulated within Riverpod providers and repositories, not in the UI layer. Standardize the project's file and folder structure. Review state management to ensure providers are scoped correctly to avoid unnecessary rebuilds. 3. **Final Cleanup**: Remove all dead or commented-out code. Use static analysis tools to identify and remove unused assets, files, and dependencies from `pubspec.yaml`. Delete all `print()` statements used for debugging and replace them with a proper logging solution if needed.", "testStrategy": "1. **Performance Benchmark**: Before starting, record performance metrics (FPS, CPU, memory usage) for key user flows (e.g., scrolling lists, navigating screens, searching). After optimization, repeat the recordings and verify a measurable improvement, aiming for a consistent 60 FPS on target devices. 2. **Code Review**: Conduct a peer code review of the refactored code. The reviewer must confirm that the changes improve code structure, readability, and maintainability without altering functionality. 3. **Static Analysis**: Run `flutter analyze` and ensure there are zero warnings or errors. 4. **Regression Testing**: Perform a full manual test of all application features, including those implemented in tasks 11, 12, 13, 14, and 15, to ensure that no new bugs were introduced during the refactoring process. All existing automated tests must continue to pass.", "status": "pending", "dependencies": [11, 12, 13, 14, 15], "priority": "medium", "subtasks": []}], "metadata": {"created": "2025-06-30T16:42:40.754Z", "updated": "2025-07-07T13:20:52.701Z", "description": "Tasks for master context"}}}