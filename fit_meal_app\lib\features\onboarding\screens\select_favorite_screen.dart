import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../providers/onboarding_provider.dart';

class SelectFavoriteScreen extends ConsumerStatefulWidget {
  final VoidCallback onNext;

  const SelectFavoriteScreen({
    super.key,
    required this.onNext,
  });

  @override
  ConsumerState<SelectFavoriteScreen> createState() => _SelectFavoriteScreenState();
}

class _SelectFavoriteScreenState extends ConsumerState<SelectFavoriteScreen> {
  // List of available favorite exercises with their icons
  final List<Map<String, String>> favoriteOptions = [
    {'name': 'Running', 'icon': 'assets/icons/running.png'},
    {'name': 'Walking', 'icon': 'assets/icons/walking.png'},
    {'name': 'Cycling', 'icon': 'assets/icons/cycling.png'},
    {'name': 'Meal plan', 'icon': 'assets/icons/mealplan.png'},
    {'name': 'Yoga', 'icon': 'assets/icons/yogas.png'},
    {'name': 'Health', 'icon': 'assets/icons/health.png'},
  ];

  // Track selected favorites locally
  List<String> selectedFavorites = [];

  void _toggleFavorite(String favorite) {
    setState(() {
      if (selectedFavorites.contains(favorite)) {
        selectedFavorites.remove(favorite);
      } else {
        selectedFavorites.add(favorite);
      }
    });

    // Update the provider with selected favorites
    ref.read(onboardingProvider.notifier).updateFavoriteExercises(selectedFavorites);
  }

  void _handleNext() {
    // Only proceed if at least one favorite is selected
    if (selectedFavorites.isNotEmpty) {
      widget.onNext();
    }
  }

  void _handleSkip() {
    // Clear selections and proceed
    selectedFavorites.clear();
    ref.read(onboardingProvider.notifier).updateFavoriteExercises([]);
    widget.onNext();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFFCFCFC),
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 22),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const SizedBox(height: 12),

              // Header with Skip button
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  const SizedBox(width: 32), // Placeholder for back button space
                  GestureDetector(
                    onTap: _handleSkip,
                    child: const Text(
                      'Skip',
                      style: TextStyle(
                        fontFamily: 'Montserrat',
                        fontSize: 14,
                        fontWeight: FontWeight.w600,
                        color: Color(0xFF00ADB5),
                      ),
                    ),
                  ),
                ],
              ),

              const SizedBox(height: 20),

              // Step indicator
              const Text(
                'Step 1 of 8',
                style: TextStyle(
                  fontFamily: 'Montserrat',
                  fontSize: 12,
                  fontWeight: FontWeight.w500,
                  color: Color(0xFF191919),
                ),
              ),

              const SizedBox(height: 8),

              // Main heading
              const Text(
                'Select Your Favorite',
                style: TextStyle(
                  fontFamily: 'Montserrat',
                  fontSize: 20,
                  fontWeight: FontWeight.w700,
                  color: Color(0xFF191919),
                ),
              ),

              const SizedBox(height: 40),

              // Favorites grid
              Expanded(
                child: GridView.builder(
                  gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                    crossAxisCount: 2,
                    crossAxisSpacing: 15,
                    mainAxisSpacing: 20,
                    childAspectRatio: 2.7, // Width to height ratio for rectangular cards
                  ),
                  itemCount: favoriteOptions.length,
                  itemBuilder: (context, index) {
                    final option = favoriteOptions[index];
                    final isSelected = selectedFavorites.contains(option['name']);

                    return FavoriteCard(
                      name: option['name']!,
                      iconPath: option['icon']!,
                      isSelected: isSelected,
                      onTap: () => _toggleFavorite(option['name']!),
                    );
                  },
                ),
              ),

              const SizedBox(height: 20),

              // Next button
              SizedBox(
                width: double.infinity,
                height: 55,
                child: ElevatedButton(
                  onPressed: selectedFavorites.isNotEmpty ? _handleNext : null,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: selectedFavorites.isNotEmpty
                        ? const Color(0xFF00ADB5)
                        : const Color(0xFFE5E5E5),
                    foregroundColor: Colors.white,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(15),
                    ),
                    elevation: 0,
                  ),
                  child: Text(
                    'Next Steps',
                    style: TextStyle(
                      fontFamily: 'Montserrat',
                      fontSize: 18,
                      fontWeight: FontWeight.w600,
                      color: selectedFavorites.isNotEmpty
                          ? Colors.white
                          : const Color(0xFF9E9E9E),
                    ),
                  ),
                ),
              ),

              const SizedBox(height: 30),
            ],
          ),
        ),
      ),
    );
  }
}

// Custom widget for favorite cards
class FavoriteCard extends StatelessWidget {
  final String name;
  final String iconPath;
  final bool isSelected;
  final VoidCallback onTap;

  const FavoriteCard({
    super.key,
    required this.name,
    required this.iconPath,
    required this.isSelected,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        decoration: BoxDecoration(
          color: isSelected ? const Color(0xFF00ADB5) : Colors.white,
          borderRadius: BorderRadius.circular(15),
          border: Border.all(
            color: isSelected ? const Color(0xFF00ADB5) : const Color(0xFFE5E5E5),
            width: 1,
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.05),
              blurRadius: 10,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          child: Row(
            children: [
              // Icon
              Image.asset(
                iconPath,
                width: 24,
                height: 24,
                color: isSelected ? Colors.white : const Color(0xFF303841),
              ),

              const SizedBox(width: 12),

              // Text
              Expanded(
                child: Text(
                  name,
                  style: TextStyle(
                    fontFamily: 'Montserrat',
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                    color: isSelected ? Colors.white : const Color(0xFF303841),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}