<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>CLIENT_ID</key>
	<string>1023047105343-meh2e2bncj4dmve7cfmm9jdbqq1j1tsm.apps.googleusercontent.com</string>
	<key>REVERSED_CLIENT_ID</key>
	<string>com.googleusercontent.apps.1023047105343-meh2e2bncj4dmve7cfmm9jdbqq1j1tsm</string>
	<key>ANDROID_CLIENT_ID</key>
	<string>1023047105343-ppkmaq7vgc21l5p9vk146qbhmn4mb5r4.apps.googleusercontent.com</string>
	<key>API_KEY</key>
	<string>AIzaSyDQ0TcPpzMFn9XOGnigADCIqHPWmQNxNd4</string>
	<key>GCM_SENDER_ID</key>
	<string>1023047105343</string>
	<key>PLIST_VERSION</key>
	<string>1</string>
	<key>BUNDLE_ID</key>
	<string>com.fitmeal.app</string>
	<key>PROJECT_ID</key>
	<string>fitmeal-app-464806</string>
	<key>STORAGE_BUCKET</key>
	<string>fitmeal-app-464806.firebasestorage.app</string>
	<key>IS_ADS_ENABLED</key>
	<false></false>
	<key>IS_ANALYTICS_ENABLED</key>
	<false></false>
	<key>IS_APPINVITE_ENABLED</key>
	<true></true>
	<key>IS_GCM_ENABLED</key>
	<true></true>
	<key>IS_SIGNIN_ENABLED</key>
	<true></true>
	<key>GOOGLE_APP_ID</key>
	<string>1:1023047105343:ios:b995aae6b7d2f842e0f18a</string>
</dict>
</plist>