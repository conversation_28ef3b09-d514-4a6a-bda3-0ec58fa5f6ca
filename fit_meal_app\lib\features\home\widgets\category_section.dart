import 'package:flutter/material.dart';

class CategorySection extends StatelessWidget {
  const CategorySection({super.key});

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            const Text(
              'Category',
              style: TextStyle(
                fontFamily: 'Montserrat',
                fontSize: 16,
                fontWeight: FontWeight.w700,
                color: Color(0xFF191919),
              ),
            ),
            TextButton(
              onPressed: () {},
              style: TextButton.styleFrom(
                padding: EdgeInsets.zero,
                minimumSize: Size.zero,
                tapTargetSize: MaterialTapTargetSize.shrinkWrap,
              ),
              child: const Text(
                'See all',
                style: TextStyle(
                  fontFamily: 'Montserrat',
                  fontSize: 12,
                  fontWeight: FontWeight.w500,
                  color: Color(0xFF00ADB5),
                ),
              ),
            ),
          ],
        ),
        const SizedBox(height: 16),
        SingleChildScrollView(
          scrollDirection: Axis.horizontal,
          child: Row(
            children: [
              CategoryItem(iconPath: 'assets/icons/yoga.png', label: 'Yoga'),
              const SizedBox(width: 40),
              CategoryItem(iconPath: 'assets/icons/gym.png', label: 'Gym'),
              const SizedBox(width: 40),
              CategoryItem(
                iconPath: 'assets/icons/cardio.png',
                label: 'Cardio',
              ),
              const SizedBox(width: 40),
              CategoryItem(
                iconPath: 'assets/icons/stretch.png',
                label: 'Stretch',
              ),
              const SizedBox(width: 40),
              CategoryItem(
                iconPath: 'assets/icons/fullbody.png',
                label: 'Full Body',
              ),
            ],
          ),
        ),
      ],
    );
  }
}

class CategoryItem extends StatelessWidget {
  final String iconPath;
  final String label;

  const CategoryItem({super.key, required this.iconPath, required this.label});

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Container(
          width: 50,
          height: 50,
          decoration: BoxDecoration(
            color: const Color(0xFFF5F5F5),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Center(
            child:
                iconPath.startsWith('assets/')
                    ? Image.asset(
                      iconPath,
                      width: 32,
                      height: 32,
                      fit: BoxFit.contain,
                      errorBuilder: (context, error, stackTrace) {
                        return const Icon(
                          Icons.fitness_center,
                          color: Color(0xFF00ADB5),
                          size: 24,
                        );
                      },
                    )
                    : const Icon(
                      Icons.fitness_center,
                      color: Color(0xFF00ADB5),
                      size: 24,
                    ),
          ),
        ),
        const SizedBox(height: 8),
        Text(
          label,
          style: const TextStyle(
            fontFamily: 'Montserrat',
            fontSize: 12,
            fontWeight: FontWeight.w600,
            color: Color(0xFF191919),
          ),
        ),
      ],
    );
  }
}
