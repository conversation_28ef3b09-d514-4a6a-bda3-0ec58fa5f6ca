import 'package:flutter/material.dart';
import 'package:fit_meal_app/features/exercise/widgets/exercise_list_section.dart';
import 'package:fit_meal_app/features/exercise/widgets/exercise_tab_section.dart';
import 'package:fit_meal_app/core/utils/size_config.dart';

class ExerciseScreen extends StatefulWidget {
  const ExerciseScreen({super.key});

  @override
  State<ExerciseScreen> createState() => _ExerciseScreenState();
}

class _ExerciseScreenState extends State<ExerciseScreen> {
  String selectedTab = 'Cardio';

  @override
  Widget build(BuildContext context) {
    SizeConfig().init(context);
    return Scaffold(
      backgroundColor: const Color(0xFFFCFCFC),
      appBar: AppBar(
        backgroundColor: const Color(0xFFFCFCFC),
        elevation: 0,
        leading: IconButton(
          icon: Image.asset(
            'assets/icons/left-arrow.png',
            width: 16,
            height: 16,
          ),
          onPressed: () => Navigator.of(context).pop(),
        ),
        title: const Text(
          'Full Exercise',
          style: TextStyle(
            fontFamily: 'Montserrat',
            fontSize: 18,
            fontWeight: FontWeight.w600,
            color: Color(0xFF191919),
          ),
        ),
        centerTitle: true,
      ),
      body: Column(
        children: [
          ExerciseTabSection(
            selectedTab: selectedTab,
            onTabChanged: (tab) {
              setState(() {
                selectedTab = tab;
              });
            },
          ),
          Expanded(child: ExerciseListSection(selectedCategory: selectedTab)),
        ],
      ),
    );
  }
}
