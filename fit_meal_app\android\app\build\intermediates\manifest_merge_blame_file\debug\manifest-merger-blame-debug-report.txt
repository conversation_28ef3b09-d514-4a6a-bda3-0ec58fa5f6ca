1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.fitmeal.app"
4    android:versionCode="1"
5    android:versionName="1.0.0" >
6
7    <uses-sdk
8        android:minSdkVersion="23"
9        android:targetSdkVersion="34" />
10    <!--
11         The INTERNET permission is required for development. Specifically,
12         the Flutter tool needs it to communicate with the running application
13         to allow setting breakpoints, to provide hot reload, etc.
14    -->
15    <uses-permission android:name="android.permission.INTERNET" />
15-->D:\FitMeal\fit_meal_app\android\app\src\debug\AndroidManifest.xml:6:5-66
15-->D:\FitMeal\fit_meal_app\android\app\src\debug\AndroidManifest.xml:6:22-64
16    <!--
17     Required to query activities that can process text, see:
18         https://developer.android.com/training/package-visibility and
19         https://developer.android.com/reference/android/content/Intent#ACTION_PROCESS_TEXT.
20
21         In particular, this is used by the Flutter engine in io.flutter.plugin.text.ProcessTextPlugin.
22    -->
23    <queries>
23-->D:\FitMeal\fit_meal_app\android\app\src\main\AndroidManifest.xml:39:5-44:15
24        <intent>
24-->D:\FitMeal\fit_meal_app\android\app\src\main\AndroidManifest.xml:40:9-43:18
25            <action android:name="android.intent.action.PROCESS_TEXT" />
25-->D:\FitMeal\fit_meal_app\android\app\src\main\AndroidManifest.xml:41:13-72
25-->D:\FitMeal\fit_meal_app\android\app\src\main\AndroidManifest.xml:41:21-70
26
27            <data android:mimeType="text/plain" />
27-->D:\FitMeal\fit_meal_app\android\app\src\main\AndroidManifest.xml:42:13-50
27-->D:\FitMeal\fit_meal_app\android\app\src\main\AndroidManifest.xml:42:19-48
28        </intent>
29        <intent>
29-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c2a986d319ef97565f43aa72d34a4af2\transformed\jetified-billing-7.1.1\AndroidManifest.xml:13:9-15:18
30            <action android:name="com.android.vending.billing.InAppBillingService.BIND" />
30-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c2a986d319ef97565f43aa72d34a4af2\transformed\jetified-billing-7.1.1\AndroidManifest.xml:14:13-91
30-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c2a986d319ef97565f43aa72d34a4af2\transformed\jetified-billing-7.1.1\AndroidManifest.xml:14:21-88
31        </intent>
32        <intent>
32-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c2a986d319ef97565f43aa72d34a4af2\transformed\jetified-billing-7.1.1\AndroidManifest.xml:16:9-18:18
33            <action android:name="com.google.android.apps.play.billingtestcompanion.BillingOverrideService.BIND" />
33-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c2a986d319ef97565f43aa72d34a4af2\transformed\jetified-billing-7.1.1\AndroidManifest.xml:17:13-116
33-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c2a986d319ef97565f43aa72d34a4af2\transformed\jetified-billing-7.1.1\AndroidManifest.xml:17:21-113
34        </intent>
35    </queries>
36
37    <uses-permission android:name="android.permission.WAKE_LOCK" />
37-->[:firebase_messaging] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_messaging-15.2.7\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:8:5-68
37-->[:firebase_messaging] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_messaging-15.2.7\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:8:22-65
38    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" /> <!-- Permissions options for the `notification` group -->
38-->[:firebase_messaging] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_messaging-15.2.7\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:9:5-79
38-->[:firebase_messaging] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_messaging-15.2.7\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:9:22-76
39    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" /> <!-- Required by older versions of Google Play services to create IID tokens -->
39-->[:firebase_messaging] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_messaging-15.2.7\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:11:5-77
39-->[:firebase_messaging] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_messaging-15.2.7\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:11:22-74
40    <uses-permission android:name="com.google.android.c2dm.permission.RECEIVE" />
40-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\91828dc6a6831ab28f967fba3340c02f\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:26:5-82
40-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\91828dc6a6831ab28f967fba3340c02f\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:26:22-79
41    <uses-permission android:name="com.google.android.providers.gsf.permission.READ_GSERVICES" />
41-->[com.google.android.recaptcha:recaptcha:18.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\655a7b34eeaa96bc78b18beba8f47601\transformed\jetified-recaptcha-18.6.1\AndroidManifest.xml:9:5-98
41-->[com.google.android.recaptcha:recaptcha:18.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\655a7b34eeaa96bc78b18beba8f47601\transformed\jetified-recaptcha-18.6.1\AndroidManifest.xml:9:22-95
42    <uses-permission android:name="com.android.vending.BILLING" />
42-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c2a986d319ef97565f43aa72d34a4af2\transformed\jetified-billing-7.1.1\AndroidManifest.xml:10:5-67
42-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c2a986d319ef97565f43aa72d34a4af2\transformed\jetified-billing-7.1.1\AndroidManifest.xml:10:22-64
43
44    <permission
44-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\76ef68f762b4d0b24e756d999cf13398\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
45        android:name="com.fitmeal.app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
45-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\76ef68f762b4d0b24e756d999cf13398\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
46        android:protectionLevel="signature" />
46-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\76ef68f762b4d0b24e756d999cf13398\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
47
48    <uses-permission android:name="com.fitmeal.app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
48-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\76ef68f762b4d0b24e756d999cf13398\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
48-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\76ef68f762b4d0b24e756d999cf13398\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
49
50    <application
51        android:name="android.app.Application"
52        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
52-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\76ef68f762b4d0b24e756d999cf13398\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
53        android:debuggable="true"
54        android:extractNativeLibs="false"
55        android:icon="@mipmap/ic_launcher"
56        android:label="FitMeal" >
57        <activity
58            android:name="com.fitmeal.app.MainActivity"
59            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
60            android:exported="true"
61            android:hardwareAccelerated="true"
62            android:launchMode="singleTop"
63            android:taskAffinity=""
64            android:theme="@style/LaunchTheme"
65            android:windowSoftInputMode="adjustResize" >
66
67            <!--
68                 Specifies an Android theme to apply to this Activity as soon as
69                 the Android process has started. This theme is visible to the user
70                 while the Flutter UI initializes. After that, this theme continues
71                 to determine the Window background behind the Flutter UI.
72            -->
73            <meta-data
74                android:name="io.flutter.embedding.android.NormalTheme"
75                android:resource="@style/NormalTheme" />
76
77            <intent-filter>
78                <action android:name="android.intent.action.MAIN" />
79
80                <category android:name="android.intent.category.LAUNCHER" />
81            </intent-filter>
82        </activity>
83        <!--
84             Don't delete the meta-data below.
85             This is used by the Flutter tool to generate GeneratedPluginRegistrant.java
86        -->
87        <meta-data
88            android:name="flutterEmbedding"
89            android:value="2" />
90
91        <receiver
91-->[com.revenuecat.purchases:purchases-store-amazon:8.20.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\56239853fb8762abcc84e7d6b23a3711\transformed\jetified-purchases-store-amazon-8.20.0\AndroidManifest.xml:8:9-15:20
92            android:name="com.amazon.device.iap.ResponseReceiver"
92-->[com.revenuecat.purchases:purchases-store-amazon:8.20.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\56239853fb8762abcc84e7d6b23a3711\transformed\jetified-purchases-store-amazon-8.20.0\AndroidManifest.xml:9:13-66
93            android:exported="true"
93-->[com.revenuecat.purchases:purchases-store-amazon:8.20.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\56239853fb8762abcc84e7d6b23a3711\transformed\jetified-purchases-store-amazon-8.20.0\AndroidManifest.xml:10:13-36
94            android:permission="com.amazon.inapp.purchasing.Permission.NOTIFY" >
94-->[com.revenuecat.purchases:purchases-store-amazon:8.20.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\56239853fb8762abcc84e7d6b23a3711\transformed\jetified-purchases-store-amazon-8.20.0\AndroidManifest.xml:11:13-79
95            <intent-filter>
95-->[com.revenuecat.purchases:purchases-store-amazon:8.20.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\56239853fb8762abcc84e7d6b23a3711\transformed\jetified-purchases-store-amazon-8.20.0\AndroidManifest.xml:12:13-14:29
96                <action android:name="com.amazon.inapp.purchasing.NOTIFY" />
96-->[com.revenuecat.purchases:purchases-store-amazon:8.20.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\56239853fb8762abcc84e7d6b23a3711\transformed\jetified-purchases-store-amazon-8.20.0\AndroidManifest.xml:13:17-77
96-->[com.revenuecat.purchases:purchases-store-amazon:8.20.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\56239853fb8762abcc84e7d6b23a3711\transformed\jetified-purchases-store-amazon-8.20.0\AndroidManifest.xml:13:25-74
97            </intent-filter>
98        </receiver>
99
100        <activity
100-->[com.revenuecat.purchases:purchases:8.20.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\13b472ba644e1a0a46b0a4c66866f991\transformed\jetified-purchases-8.20.0\AndroidManifest.xml:10:9-13:75
101            android:name="com.revenuecat.purchases.amazon.purchasing.ProxyAmazonBillingActivity"
101-->[com.revenuecat.purchases:purchases:8.20.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\13b472ba644e1a0a46b0a4c66866f991\transformed\jetified-purchases-8.20.0\AndroidManifest.xml:11:13-97
102            android:configChanges="keyboard|keyboardHidden|screenLayout|screenSize|orientation"
102-->[com.revenuecat.purchases:purchases:8.20.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\13b472ba644e1a0a46b0a4c66866f991\transformed\jetified-purchases-8.20.0\AndroidManifest.xml:12:13-96
103            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
103-->[com.revenuecat.purchases:purchases:8.20.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\13b472ba644e1a0a46b0a4c66866f991\transformed\jetified-purchases-8.20.0\AndroidManifest.xml:13:13-72
104
105        <service
105-->[:firebase_auth] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_auth-5.6.0\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:8:9-12:19
106            android:name="com.google.firebase.components.ComponentDiscoveryService"
106-->[:firebase_auth] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_auth-5.6.0\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:8:18-89
107            android:directBootAware="true"
107-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\43e6a54707a9cd0267e1aef95e5d6677\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:32:13-43
108            android:exported="false" >
108-->[com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7294f6d93ef2c2f30edfdcaefa377cb2\transformed\jetified-firebase-auth-23.2.0\AndroidManifest.xml:68:13-37
109            <meta-data
109-->[:firebase_auth] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_auth-5.6.0\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:9:13-11:85
110                android:name="com.google.firebase.components:io.flutter.plugins.firebase.auth.FlutterFirebaseAuthRegistrar"
110-->[:firebase_auth] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_auth-5.6.0\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:10:17-124
111                android:value="com.google.firebase.components.ComponentRegistrar" />
111-->[:firebase_auth] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_auth-5.6.0\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:11:17-82
112            <meta-data
112-->[:firebase_messaging] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_messaging-15.2.7\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:36:13-38:85
113                android:name="com.google.firebase.components:io.flutter.plugins.firebase.messaging.FlutterFirebaseAppRegistrar"
113-->[:firebase_messaging] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_messaging-15.2.7\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:37:17-128
114                android:value="com.google.firebase.components.ComponentRegistrar" />
114-->[:firebase_messaging] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_messaging-15.2.7\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:38:17-82
115            <meta-data
115-->[:firebase_core] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_core-3.14.0\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:9:13-11:85
116                android:name="com.google.firebase.components:io.flutter.plugins.firebase.core.FlutterFirebaseCoreRegistrar"
116-->[:firebase_core] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_core-3.14.0\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:10:17-124
117                android:value="com.google.firebase.components.ComponentRegistrar" />
117-->[:firebase_core] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_core-3.14.0\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:11:17-82
118            <meta-data
118-->[com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7294f6d93ef2c2f30edfdcaefa377cb2\transformed\jetified-firebase-auth-23.2.0\AndroidManifest.xml:69:13-71:85
119                android:name="com.google.firebase.components:com.google.firebase.auth.FirebaseAuthRegistrar"
119-->[com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7294f6d93ef2c2f30edfdcaefa377cb2\transformed\jetified-firebase-auth-23.2.0\AndroidManifest.xml:70:17-109
120                android:value="com.google.firebase.components.ComponentRegistrar" />
120-->[com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7294f6d93ef2c2f30edfdcaefa377cb2\transformed\jetified-firebase-auth-23.2.0\AndroidManifest.xml:71:17-82
121            <meta-data
121-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\91828dc6a6831ab28f967fba3340c02f\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:57:13-59:85
122                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingKtxRegistrar"
122-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\91828dc6a6831ab28f967fba3340c02f\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:58:17-122
123                android:value="com.google.firebase.components.ComponentRegistrar" />
123-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\91828dc6a6831ab28f967fba3340c02f\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:59:17-82
124            <meta-data
124-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\91828dc6a6831ab28f967fba3340c02f\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:60:13-62:85
125                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingRegistrar"
125-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\91828dc6a6831ab28f967fba3340c02f\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:61:17-119
126                android:value="com.google.firebase.components.ComponentRegistrar" />
126-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\91828dc6a6831ab28f967fba3340c02f\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:62:17-82
127            <meta-data
127-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b5dad2b6b663195da17f2d25c14a14dc\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:15:13-17:85
128                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsKtxRegistrar"
128-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b5dad2b6b663195da17f2d25c14a14dc\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:16:17-130
129                android:value="com.google.firebase.components.ComponentRegistrar" />
129-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b5dad2b6b663195da17f2d25c14a14dc\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:17:17-82
130            <meta-data
130-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b5dad2b6b663195da17f2d25c14a14dc\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:18:13-20:85
131                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar"
131-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b5dad2b6b663195da17f2d25c14a14dc\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:19:17-127
132                android:value="com.google.firebase.components.ComponentRegistrar" />
132-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b5dad2b6b663195da17f2d25c14a14dc\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:20:17-82
133            <meta-data
133-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e62ac1b5f28d21aa5657f85c3325f951\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:12:13-14:85
134                android:name="com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar"
134-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e62ac1b5f28d21aa5657f85c3325f951\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:13:17-116
135                android:value="com.google.firebase.components.ComponentRegistrar" />
135-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e62ac1b5f28d21aa5657f85c3325f951\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:14:17-82
136            <meta-data
136-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\43e6a54707a9cd0267e1aef95e5d6677\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:35:13-37:85
137                android:name="com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar"
137-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\43e6a54707a9cd0267e1aef95e5d6677\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:36:17-109
138                android:value="com.google.firebase.components.ComponentRegistrar" />
138-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\43e6a54707a9cd0267e1aef95e5d6677\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:37:17-82
139            <meta-data
139-->[com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\61eda0286457717b4e92c1d0fa586500\transformed\jetified-firebase-datatransport-18.2.0\AndroidManifest.xml:25:13-27:85
140                android:name="com.google.firebase.components:com.google.firebase.datatransport.TransportRegistrar"
140-->[com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\61eda0286457717b4e92c1d0fa586500\transformed\jetified-firebase-datatransport-18.2.0\AndroidManifest.xml:26:17-115
141                android:value="com.google.firebase.components.ComponentRegistrar" />
141-->[com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\61eda0286457717b4e92c1d0fa586500\transformed\jetified-firebase-datatransport-18.2.0\AndroidManifest.xml:27:17-82
142        </service>
143        <service
143-->[:firebase_messaging] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_messaging-15.2.7\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:14:9-17:72
144            android:name="io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingBackgroundService"
144-->[:firebase_messaging] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_messaging-15.2.7\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:15:13-107
145            android:exported="false"
145-->[:firebase_messaging] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_messaging-15.2.7\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:16:13-37
146            android:permission="android.permission.BIND_JOB_SERVICE" />
146-->[:firebase_messaging] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_messaging-15.2.7\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:17:13-69
147        <service
147-->[:firebase_messaging] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_messaging-15.2.7\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:18:9-24:19
148            android:name="io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingService"
148-->[:firebase_messaging] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_messaging-15.2.7\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:19:13-97
149            android:exported="false" >
149-->[:firebase_messaging] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_messaging-15.2.7\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:20:13-37
150            <intent-filter>
150-->[:firebase_messaging] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_messaging-15.2.7\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:21:13-23:29
151                <action android:name="com.google.firebase.MESSAGING_EVENT" />
151-->[:firebase_messaging] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_messaging-15.2.7\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:22:17-78
151-->[:firebase_messaging] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_messaging-15.2.7\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:22:25-75
152            </intent-filter>
153        </service>
154
155        <receiver
155-->[:firebase_messaging] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_messaging-15.2.7\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:26:9-33:20
156            android:name="io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingReceiver"
156-->[:firebase_messaging] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_messaging-15.2.7\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:27:13-98
157            android:exported="true"
157-->[:firebase_messaging] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_messaging-15.2.7\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:28:13-36
158            android:permission="com.google.android.c2dm.permission.SEND" >
158-->[:firebase_messaging] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_messaging-15.2.7\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:29:13-73
159            <intent-filter>
159-->[:firebase_messaging] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_messaging-15.2.7\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:30:13-32:29
160                <action android:name="com.google.android.c2dm.intent.RECEIVE" />
160-->[:firebase_messaging] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_messaging-15.2.7\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:31:17-81
160-->[:firebase_messaging] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_messaging-15.2.7\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:31:25-78
161            </intent-filter>
162        </receiver>
163
164        <provider
164-->[:firebase_messaging] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_messaging-15.2.7\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:41:9-45:38
165            android:name="io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingInitProvider"
165-->[:firebase_messaging] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_messaging-15.2.7\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:42:13-102
166            android:authorities="com.fitmeal.app.flutterfirebasemessaginginitprovider"
166-->[:firebase_messaging] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_messaging-15.2.7\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:43:13-88
167            android:exported="false"
167-->[:firebase_messaging] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_messaging-15.2.7\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:44:13-37
168            android:initOrder="99" />
168-->[:firebase_messaging] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_messaging-15.2.7\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:45:13-35
169
170        <activity
170-->[com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7294f6d93ef2c2f30edfdcaefa377cb2\transformed\jetified-firebase-auth-23.2.0\AndroidManifest.xml:29:9-46:20
171            android:name="com.google.firebase.auth.internal.GenericIdpActivity"
171-->[com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7294f6d93ef2c2f30edfdcaefa377cb2\transformed\jetified-firebase-auth-23.2.0\AndroidManifest.xml:30:13-80
172            android:excludeFromRecents="true"
172-->[com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7294f6d93ef2c2f30edfdcaefa377cb2\transformed\jetified-firebase-auth-23.2.0\AndroidManifest.xml:31:13-46
173            android:exported="true"
173-->[com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7294f6d93ef2c2f30edfdcaefa377cb2\transformed\jetified-firebase-auth-23.2.0\AndroidManifest.xml:32:13-36
174            android:launchMode="singleTask"
174-->[com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7294f6d93ef2c2f30edfdcaefa377cb2\transformed\jetified-firebase-auth-23.2.0\AndroidManifest.xml:33:13-44
175            android:theme="@android:style/Theme.Translucent.NoTitleBar" >
175-->[com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7294f6d93ef2c2f30edfdcaefa377cb2\transformed\jetified-firebase-auth-23.2.0\AndroidManifest.xml:34:13-72
176            <intent-filter>
176-->[com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7294f6d93ef2c2f30edfdcaefa377cb2\transformed\jetified-firebase-auth-23.2.0\AndroidManifest.xml:35:13-45:29
177                <action android:name="android.intent.action.VIEW" />
177-->[com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7294f6d93ef2c2f30edfdcaefa377cb2\transformed\jetified-firebase-auth-23.2.0\AndroidManifest.xml:36:17-69
177-->[com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7294f6d93ef2c2f30edfdcaefa377cb2\transformed\jetified-firebase-auth-23.2.0\AndroidManifest.xml:36:25-66
178
179                <category android:name="android.intent.category.DEFAULT" />
179-->[com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7294f6d93ef2c2f30edfdcaefa377cb2\transformed\jetified-firebase-auth-23.2.0\AndroidManifest.xml:38:17-76
179-->[com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7294f6d93ef2c2f30edfdcaefa377cb2\transformed\jetified-firebase-auth-23.2.0\AndroidManifest.xml:38:27-73
180                <category android:name="android.intent.category.BROWSABLE" />
180-->[com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7294f6d93ef2c2f30edfdcaefa377cb2\transformed\jetified-firebase-auth-23.2.0\AndroidManifest.xml:39:17-78
180-->[com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7294f6d93ef2c2f30edfdcaefa377cb2\transformed\jetified-firebase-auth-23.2.0\AndroidManifest.xml:39:27-75
181
182                <data
182-->D:\FitMeal\fit_meal_app\android\app\src\main\AndroidManifest.xml:42:13-50
183                    android:host="firebase.auth"
184                    android:path="/"
185                    android:scheme="genericidp" />
186            </intent-filter>
187        </activity>
188        <activity
188-->[com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7294f6d93ef2c2f30edfdcaefa377cb2\transformed\jetified-firebase-auth-23.2.0\AndroidManifest.xml:47:9-64:20
189            android:name="com.google.firebase.auth.internal.RecaptchaActivity"
189-->[com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7294f6d93ef2c2f30edfdcaefa377cb2\transformed\jetified-firebase-auth-23.2.0\AndroidManifest.xml:48:13-79
190            android:excludeFromRecents="true"
190-->[com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7294f6d93ef2c2f30edfdcaefa377cb2\transformed\jetified-firebase-auth-23.2.0\AndroidManifest.xml:49:13-46
191            android:exported="true"
191-->[com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7294f6d93ef2c2f30edfdcaefa377cb2\transformed\jetified-firebase-auth-23.2.0\AndroidManifest.xml:50:13-36
192            android:launchMode="singleTask"
192-->[com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7294f6d93ef2c2f30edfdcaefa377cb2\transformed\jetified-firebase-auth-23.2.0\AndroidManifest.xml:51:13-44
193            android:theme="@android:style/Theme.Translucent.NoTitleBar" >
193-->[com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7294f6d93ef2c2f30edfdcaefa377cb2\transformed\jetified-firebase-auth-23.2.0\AndroidManifest.xml:52:13-72
194            <intent-filter>
194-->[com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7294f6d93ef2c2f30edfdcaefa377cb2\transformed\jetified-firebase-auth-23.2.0\AndroidManifest.xml:53:13-63:29
195                <action android:name="android.intent.action.VIEW" />
195-->[com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7294f6d93ef2c2f30edfdcaefa377cb2\transformed\jetified-firebase-auth-23.2.0\AndroidManifest.xml:36:17-69
195-->[com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7294f6d93ef2c2f30edfdcaefa377cb2\transformed\jetified-firebase-auth-23.2.0\AndroidManifest.xml:36:25-66
196
197                <category android:name="android.intent.category.DEFAULT" />
197-->[com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7294f6d93ef2c2f30edfdcaefa377cb2\transformed\jetified-firebase-auth-23.2.0\AndroidManifest.xml:38:17-76
197-->[com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7294f6d93ef2c2f30edfdcaefa377cb2\transformed\jetified-firebase-auth-23.2.0\AndroidManifest.xml:38:27-73
198                <category android:name="android.intent.category.BROWSABLE" />
198-->[com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7294f6d93ef2c2f30edfdcaefa377cb2\transformed\jetified-firebase-auth-23.2.0\AndroidManifest.xml:39:17-78
198-->[com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7294f6d93ef2c2f30edfdcaefa377cb2\transformed\jetified-firebase-auth-23.2.0\AndroidManifest.xml:39:27-75
199
200                <data
200-->D:\FitMeal\fit_meal_app\android\app\src\main\AndroidManifest.xml:42:13-50
201                    android:host="firebase.auth"
202                    android:path="/"
203                    android:scheme="recaptcha" />
204            </intent-filter>
205        </activity>
206
207        <receiver
207-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\91828dc6a6831ab28f967fba3340c02f\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:29:9-40:20
208            android:name="com.google.firebase.iid.FirebaseInstanceIdReceiver"
208-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\91828dc6a6831ab28f967fba3340c02f\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:30:13-78
209            android:exported="true"
209-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\91828dc6a6831ab28f967fba3340c02f\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:31:13-36
210            android:permission="com.google.android.c2dm.permission.SEND" >
210-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\91828dc6a6831ab28f967fba3340c02f\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:32:13-73
211            <intent-filter>
211-->[:firebase_messaging] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_messaging-15.2.7\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:30:13-32:29
212                <action android:name="com.google.android.c2dm.intent.RECEIVE" />
212-->[:firebase_messaging] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_messaging-15.2.7\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:31:17-81
212-->[:firebase_messaging] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_messaging-15.2.7\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:31:25-78
213            </intent-filter>
214
215            <meta-data
215-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\91828dc6a6831ab28f967fba3340c02f\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:37:13-39:40
216                android:name="com.google.android.gms.cloudmessaging.FINISHED_AFTER_HANDLED"
216-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\91828dc6a6831ab28f967fba3340c02f\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:38:17-92
217                android:value="true" />
217-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\91828dc6a6831ab28f967fba3340c02f\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:39:17-37
218        </receiver>
219        <!--
220             FirebaseMessagingService performs security checks at runtime,
221             but set to not exported to explicitly avoid allowing another app to call it.
222        -->
223        <service
223-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\91828dc6a6831ab28f967fba3340c02f\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:46:9-53:19
224            android:name="com.google.firebase.messaging.FirebaseMessagingService"
224-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\91828dc6a6831ab28f967fba3340c02f\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:47:13-82
225            android:directBootAware="true"
225-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\91828dc6a6831ab28f967fba3340c02f\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:48:13-43
226            android:exported="false" >
226-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\91828dc6a6831ab28f967fba3340c02f\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:49:13-37
227            <intent-filter android:priority="-500" >
227-->[:firebase_messaging] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_messaging-15.2.7\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:21:13-23:29
228                <action android:name="com.google.firebase.MESSAGING_EVENT" />
228-->[:firebase_messaging] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_messaging-15.2.7\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:22:17-78
228-->[:firebase_messaging] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_messaging-15.2.7\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:22:25-75
229            </intent-filter>
230        </service>
231
232        <activity
232-->[:url_launcher_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\url_launcher_android-6.3.16\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:8:9-11:74
233            android:name="io.flutter.plugins.urllauncher.WebViewActivity"
233-->[:url_launcher_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\url_launcher_android-6.3.16\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:9:13-74
234            android:exported="false"
234-->[:url_launcher_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\url_launcher_android-6.3.16\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:10:13-37
235            android:theme="@android:style/Theme.NoTitleBar.Fullscreen" />
235-->[:url_launcher_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\url_launcher_android-6.3.16\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:11:13-71
236
237        <uses-library
237-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\47e0a69f678750b06c1a6f4d202b19ad\transformed\jetified-window-1.2.0\AndroidManifest.xml:23:9-25:40
238            android:name="androidx.window.extensions"
238-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\47e0a69f678750b06c1a6f4d202b19ad\transformed\jetified-window-1.2.0\AndroidManifest.xml:24:13-54
239            android:required="false" />
239-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\47e0a69f678750b06c1a6f4d202b19ad\transformed\jetified-window-1.2.0\AndroidManifest.xml:25:13-37
240        <uses-library
240-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\47e0a69f678750b06c1a6f4d202b19ad\transformed\jetified-window-1.2.0\AndroidManifest.xml:26:9-28:40
241            android:name="androidx.window.sidecar"
241-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\47e0a69f678750b06c1a6f4d202b19ad\transformed\jetified-window-1.2.0\AndroidManifest.xml:27:13-51
242            android:required="false" />
242-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\47e0a69f678750b06c1a6f4d202b19ad\transformed\jetified-window-1.2.0\AndroidManifest.xml:28:13-37
243
244        <service
244-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\34f29e0d4a938cee4c3a6332c7d3494f\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:24:9-32:19
245            android:name="androidx.credentials.playservices.CredentialProviderMetadataHolder"
245-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\34f29e0d4a938cee4c3a6332c7d3494f\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:25:13-94
246            android:enabled="true"
246-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\34f29e0d4a938cee4c3a6332c7d3494f\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:26:13-35
247            android:exported="false" >
247-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\34f29e0d4a938cee4c3a6332c7d3494f\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:27:13-37
248            <meta-data
248-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\34f29e0d4a938cee4c3a6332c7d3494f\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:29:13-31:104
249                android:name="androidx.credentials.CREDENTIAL_PROVIDER_KEY"
249-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\34f29e0d4a938cee4c3a6332c7d3494f\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:30:17-76
250                android:value="androidx.credentials.playservices.CredentialProviderPlayServicesImpl" />
250-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\34f29e0d4a938cee4c3a6332c7d3494f\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:31:17-101
251        </service>
252
253        <activity
253-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\34f29e0d4a938cee4c3a6332c7d3494f\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:34:9-41:20
254            android:name="androidx.credentials.playservices.HiddenActivity"
254-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\34f29e0d4a938cee4c3a6332c7d3494f\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:35:13-76
255            android:configChanges="orientation|screenSize|screenLayout|keyboardHidden"
255-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\34f29e0d4a938cee4c3a6332c7d3494f\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:36:13-87
256            android:enabled="true"
256-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\34f29e0d4a938cee4c3a6332c7d3494f\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:37:13-35
257            android:exported="false"
257-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\34f29e0d4a938cee4c3a6332c7d3494f\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:38:13-37
258            android:fitsSystemWindows="true"
258-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\34f29e0d4a938cee4c3a6332c7d3494f\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:39:13-45
259            android:theme="@style/Theme.Hidden" >
259-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\34f29e0d4a938cee4c3a6332c7d3494f\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:40:13-48
260        </activity>
261        <activity
261-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4defe6f683cb8137150a75a7c76411a5\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:23:9-27:75
262            android:name="com.google.android.gms.auth.api.signin.internal.SignInHubActivity"
262-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4defe6f683cb8137150a75a7c76411a5\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:24:13-93
263            android:excludeFromRecents="true"
263-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4defe6f683cb8137150a75a7c76411a5\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:25:13-46
264            android:exported="false"
264-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4defe6f683cb8137150a75a7c76411a5\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:26:13-37
265            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
265-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4defe6f683cb8137150a75a7c76411a5\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:27:13-72
266        <!--
267            Service handling Google Sign-In user revocation. For apps that do not integrate with
268            Google Sign-In, this service will never be started.
269        -->
270        <service
270-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4defe6f683cb8137150a75a7c76411a5\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:33:9-37:51
271            android:name="com.google.android.gms.auth.api.signin.RevocationBoundService"
271-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4defe6f683cb8137150a75a7c76411a5\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:34:13-89
272            android:exported="true"
272-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4defe6f683cb8137150a75a7c76411a5\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:35:13-36
273            android:permission="com.google.android.gms.auth.api.signin.permission.REVOCATION_NOTIFICATION"
273-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4defe6f683cb8137150a75a7c76411a5\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:36:13-107
274            android:visibleToInstantApps="true" />
274-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4defe6f683cb8137150a75a7c76411a5\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:37:13-48
275
276        <provider
276-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\43e6a54707a9cd0267e1aef95e5d6677\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:23:9-28:39
277            android:name="com.google.firebase.provider.FirebaseInitProvider"
277-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\43e6a54707a9cd0267e1aef95e5d6677\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:24:13-77
278            android:authorities="com.fitmeal.app.firebaseinitprovider"
278-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\43e6a54707a9cd0267e1aef95e5d6677\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:25:13-72
279            android:directBootAware="true"
279-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\43e6a54707a9cd0267e1aef95e5d6677\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:26:13-43
280            android:exported="false"
280-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\43e6a54707a9cd0267e1aef95e5d6677\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:27:13-37
281            android:initOrder="100" />
281-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\43e6a54707a9cd0267e1aef95e5d6677\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:28:13-36
282
283        <meta-data
283-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c2a986d319ef97565f43aa72d34a4af2\transformed\jetified-billing-7.1.1\AndroidManifest.xml:22:9-24:37
284            android:name="com.google.android.play.billingclient.version"
284-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c2a986d319ef97565f43aa72d34a4af2\transformed\jetified-billing-7.1.1\AndroidManifest.xml:23:13-73
285            android:value="7.1.1" />
285-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c2a986d319ef97565f43aa72d34a4af2\transformed\jetified-billing-7.1.1\AndroidManifest.xml:24:13-34
286
287        <activity
287-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c2a986d319ef97565f43aa72d34a4af2\transformed\jetified-billing-7.1.1\AndroidManifest.xml:26:9-30:75
288            android:name="com.android.billingclient.api.ProxyBillingActivity"
288-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c2a986d319ef97565f43aa72d34a4af2\transformed\jetified-billing-7.1.1\AndroidManifest.xml:27:13-78
289            android:configChanges="keyboard|keyboardHidden|screenLayout|screenSize|orientation"
289-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c2a986d319ef97565f43aa72d34a4af2\transformed\jetified-billing-7.1.1\AndroidManifest.xml:28:13-96
290            android:exported="false"
290-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c2a986d319ef97565f43aa72d34a4af2\transformed\jetified-billing-7.1.1\AndroidManifest.xml:29:13-37
291            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
291-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c2a986d319ef97565f43aa72d34a4af2\transformed\jetified-billing-7.1.1\AndroidManifest.xml:30:13-72
292        <activity
292-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c2a986d319ef97565f43aa72d34a4af2\transformed\jetified-billing-7.1.1\AndroidManifest.xml:31:9-35:75
293            android:name="com.android.billingclient.api.ProxyBillingActivityV2"
293-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c2a986d319ef97565f43aa72d34a4af2\transformed\jetified-billing-7.1.1\AndroidManifest.xml:32:13-80
294            android:configChanges="keyboard|keyboardHidden|screenLayout|screenSize|orientation"
294-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c2a986d319ef97565f43aa72d34a4af2\transformed\jetified-billing-7.1.1\AndroidManifest.xml:33:13-96
295            android:exported="false"
295-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c2a986d319ef97565f43aa72d34a4af2\transformed\jetified-billing-7.1.1\AndroidManifest.xml:34:13-37
296            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
296-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c2a986d319ef97565f43aa72d34a4af2\transformed\jetified-billing-7.1.1\AndroidManifest.xml:35:13-72
297
298        <provider
298-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2a71a5a7fb7753509b2953ab65f8bf38\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:24:9-32:20
299            android:name="androidx.startup.InitializationProvider"
299-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2a71a5a7fb7753509b2953ab65f8bf38\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:25:13-67
300            android:authorities="com.fitmeal.app.androidx-startup"
300-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2a71a5a7fb7753509b2953ab65f8bf38\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:26:13-68
301            android:exported="false" >
301-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2a71a5a7fb7753509b2953ab65f8bf38\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:27:13-37
302            <meta-data
302-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2a71a5a7fb7753509b2953ab65f8bf38\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
303                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
303-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2a71a5a7fb7753509b2953ab65f8bf38\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
304                android:value="androidx.startup" />
304-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2a71a5a7fb7753509b2953ab65f8bf38\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
305            <meta-data
305-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0ac80c548d8ad76b648fb8f8c1173991\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
306                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
306-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0ac80c548d8ad76b648fb8f8c1173991\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
307                android:value="androidx.startup" />
307-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0ac80c548d8ad76b648fb8f8c1173991\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
308        </provider>
309
310        <activity
310-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3115c143cfae3e078982e1b1654bca2e\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:9-173
311            android:name="com.google.android.gms.common.api.GoogleApiActivity"
311-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3115c143cfae3e078982e1b1654bca2e\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:19-85
312            android:exported="false"
312-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3115c143cfae3e078982e1b1654bca2e\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:146-170
313            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
313-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3115c143cfae3e078982e1b1654bca2e\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:86-145
314
315        <meta-data
315-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\06fac09f8999fc4efa604af55bfbe4d8\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:6:9-122
316            android:name="com.google.android.gms.version"
316-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\06fac09f8999fc4efa604af55bfbe4d8\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:6:20-65
317            android:value="@integer/google_play_services_version" />
317-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\06fac09f8999fc4efa604af55bfbe4d8\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:6:66-119
318
319        <receiver
319-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0ac80c548d8ad76b648fb8f8c1173991\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
320            android:name="androidx.profileinstaller.ProfileInstallReceiver"
320-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0ac80c548d8ad76b648fb8f8c1173991\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
321            android:directBootAware="false"
321-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0ac80c548d8ad76b648fb8f8c1173991\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
322            android:enabled="true"
322-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0ac80c548d8ad76b648fb8f8c1173991\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
323            android:exported="true"
323-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0ac80c548d8ad76b648fb8f8c1173991\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
324            android:permission="android.permission.DUMP" >
324-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0ac80c548d8ad76b648fb8f8c1173991\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
325            <intent-filter>
325-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0ac80c548d8ad76b648fb8f8c1173991\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
326                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
326-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0ac80c548d8ad76b648fb8f8c1173991\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
326-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0ac80c548d8ad76b648fb8f8c1173991\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
327            </intent-filter>
328            <intent-filter>
328-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0ac80c548d8ad76b648fb8f8c1173991\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
329                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
329-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0ac80c548d8ad76b648fb8f8c1173991\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
329-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0ac80c548d8ad76b648fb8f8c1173991\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
330            </intent-filter>
331            <intent-filter>
331-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0ac80c548d8ad76b648fb8f8c1173991\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
332                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
332-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0ac80c548d8ad76b648fb8f8c1173991\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
332-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0ac80c548d8ad76b648fb8f8c1173991\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
333            </intent-filter>
334            <intent-filter>
334-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0ac80c548d8ad76b648fb8f8c1173991\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
335                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
335-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0ac80c548d8ad76b648fb8f8c1173991\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
335-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0ac80c548d8ad76b648fb8f8c1173991\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
336            </intent-filter>
337        </receiver>
338
339        <service
339-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\06236119eb61e4883552fd9f864c58cc\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:28:9-34:19
340            android:name="com.google.android.datatransport.runtime.backends.TransportBackendDiscovery"
340-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\06236119eb61e4883552fd9f864c58cc\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:29:13-103
341            android:exported="false" >
341-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\06236119eb61e4883552fd9f864c58cc\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:30:13-37
342            <meta-data
342-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\06236119eb61e4883552fd9f864c58cc\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:31:13-33:39
343                android:name="backend:com.google.android.datatransport.cct.CctBackendFactory"
343-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\06236119eb61e4883552fd9f864c58cc\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:32:17-94
344                android:value="cct" />
344-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\06236119eb61e4883552fd9f864c58cc\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:33:17-36
345        </service>
346        <service
346-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\62250bfa96ec51041fefa9d4666f8436\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:26:9-30:19
347            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService"
347-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\62250bfa96ec51041fefa9d4666f8436\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:27:13-117
348            android:exported="false"
348-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\62250bfa96ec51041fefa9d4666f8436\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:28:13-37
349            android:permission="android.permission.BIND_JOB_SERVICE" >
349-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\62250bfa96ec51041fefa9d4666f8436\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:29:13-69
350        </service>
351
352        <receiver
352-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\62250bfa96ec51041fefa9d4666f8436\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:32:9-34:40
353            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver"
353-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\62250bfa96ec51041fefa9d4666f8436\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:33:13-132
354            android:exported="false" /> <!-- The activities will be merged into the manifest of the hosting app. -->
354-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\62250bfa96ec51041fefa9d4666f8436\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:34:13-37
355        <activity
355-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1ad56212abf1e8d39b5df2d68dd12667\transformed\jetified-core-common-2.0.3\AndroidManifest.xml:14:9-18:65
356            android:name="com.google.android.play.core.common.PlayCoreDialogWrapperActivity"
356-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1ad56212abf1e8d39b5df2d68dd12667\transformed\jetified-core-common-2.0.3\AndroidManifest.xml:15:13-93
357            android:exported="false"
357-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1ad56212abf1e8d39b5df2d68dd12667\transformed\jetified-core-common-2.0.3\AndroidManifest.xml:16:13-37
358            android:stateNotNeeded="true"
358-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1ad56212abf1e8d39b5df2d68dd12667\transformed\jetified-core-common-2.0.3\AndroidManifest.xml:17:13-42
359            android:theme="@style/Theme.PlayCore.Transparent" />
359-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1ad56212abf1e8d39b5df2d68dd12667\transformed\jetified-core-common-2.0.3\AndroidManifest.xml:18:13-62
360    </application>
361
362</manifest>
