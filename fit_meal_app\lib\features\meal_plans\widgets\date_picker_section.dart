import 'package:flutter/material.dart';

class DatePickerSection extends StatefulWidget {
  const DatePickerSection({super.key});

  @override
  State<DatePickerSection> createState() => _DatePickerSectionState();
}

class _DatePickerSectionState extends State<DatePickerSection> {
  int selectedDay = 20;

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 20),
      child: Column(
        children: [
          // Month and Year with navigation
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // Left arrow
              GestureDetector(
                onTap: () {
                  // Previous month
                },
                child: Container(
                  width: 24,
                  height: 24,
                  alignment: Alignment.center,
                  child: Image.asset(
                    'assets/icons/left-arrow.png',
                    width: 16,
                    height: 16,
                  ),
                ),
              ),

              const SizedBox(width: 16),

              // Month and Year
              Column(
                children: [
                  const Text(
                    'February',
                    style: TextStyle(
                      fontFamily: 'Montserrat',
                      fontWeight: FontWeight.w600,
                      fontSize: 14,
                      color: Color(0xFF191919),
                      letterSpacing: 0,
                      height: 1.22,
                    ),
                  ),
                  const SizedBox(height: 4),
                  const Text(
                    '2022',
                    style: TextStyle(
                      fontFamily: 'Montserrat',
                      fontWeight: FontWeight.w500,
                      fontSize: 12,
                      color: Color(0xFF3A4750),
                      letterSpacing: 0,
                      height: 1.22,
                    ),
                  ),
                ],
              ),

              const SizedBox(width: 16),

              // Right arrow
              GestureDetector(
                onTap: () {
                  // Next month
                },
                child: Container(
                  width: 24,
                  height: 24,
                  alignment: Alignment.center,
                  child: Image.asset(
                    'assets/icons/right-arrow.png',
                    width: 16,
                    height: 16,
                  ),
                ),
              ),
            ],
          ),

          const SizedBox(height: 24),

          // Weekly Calendar
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              _buildDayColumn('Sun', 20, selectedDay == 20),
              _buildDayColumn('Mon', 21, selectedDay == 21),
              _buildDayColumn('Tue', 22, selectedDay == 22),
              _buildDayColumn('Wed', 23, selectedDay == 23),
              _buildDayColumn('Thu', 24, selectedDay == 24),
              _buildDayColumn('Fri', 25, selectedDay == 25),
            ],
          ),

          const SizedBox(height: 16),
        ],
      ),
    );
  }

  Widget _buildDayColumn(String dayName, int dayNumber, bool isSelected) {
    return Container(
      width: 50,
      height: 80,
      margin: const EdgeInsets.symmetric(horizontal: 5),
      child: GestureDetector(
        onTap: () {
          setState(() {
            selectedDay = dayNumber;
          });
        },
        child: Container(
          decoration: BoxDecoration(
            color:
                isSelected ? const Color(0xFF00ADB5) : const Color(0xFFF5F5F5),
            borderRadius: BorderRadius.circular(6),
          ),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(
                dayName,
                style: TextStyle(
                  fontFamily: 'Montserrat',
                  fontWeight: FontWeight.w600,
                  fontSize: 14,
                  color: isSelected ? Colors.white : const Color(0xFF3A4750),
                  letterSpacing: 0,
                  height: 1.22,
                ),
              ),
              const SizedBox(height: 8),
              Container(
                width: 32,
                height: 32,
                decoration: BoxDecoration(
                  color: Colors.white,
                  shape: BoxShape.circle,
                ),
                child: Center(
                  child: Text(
                    dayNumber.toString(),
                    style: TextStyle(
                      fontFamily: 'Montserrat',
                      fontWeight: FontWeight.w700,
                      fontSize: 14,
                      color:
                          isSelected
                              ? const Color(0xFF00ADB5)
                              : const Color(0xFF191919),
                      letterSpacing: 0,
                      height: 1.22,
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
