import 'package:flutter/material.dart';

class MealPlansSection extends StatelessWidget {
  const MealPlansSection({super.key});

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            const Text(
              'Meal Plans',
              style: TextStyle(
                fontFamily: 'Montserrat',
                fontSize: 16,
                fontWeight: FontWeight.w700,
                color: Color(0xFF191919),
              ),
            ),
            TextButton(
              onPressed: () {},
              style: TextButton.styleFrom(
                padding: EdgeInsets.zero,
                minimumSize: Size.zero,
                tapTargetSize: MaterialTapTargetSize.shrinkWrap,
              ),
              child: const Text(
                'See all',
                style: TextStyle(
                  fontFamily: 'Montserrat',
                  fontSize: 12,
                  fontWeight: FontWeight.w500,
                  color: Color(0xFF00ADB5),
                ),
              ),
            ),
          ],
        ),
        const SizedBox(height: 10),
        MealCard(
          title: 'Greek salad with lettuce, green onion, ',
          calories: '150 kcal',
          imageUrl:
              'https://images.unsplash.com/photo-1512621776951-a57141f2eefd',
        ),
        const Divider(color: Color(0xFFE5E5E5), thickness: 1, height: 1),
        const SizedBox(height: 10),
        MealCard(
          title: 'Salad of fresh vegetables',
          calories: '270 kcal',
          imageUrl:
              'https://images.unsplash.com/photo-1504674900247-0877df9cc836?auto=format&fit=crop&w=800&q=80',
        ),
      ],
    );
  }
}

class MealCard extends StatelessWidget {
  final String title;
  final String calories;
  final String imageUrl;

  const MealCard({
    super.key,
    required this.title,
    required this.calories,
    required this.imageUrl,
  });

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: 256,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            width: double.infinity,
            height: 180,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(15),
              image: DecorationImage(
                image: NetworkImage(imageUrl),
                fit: BoxFit.cover,
              ),
            ),
          ),
          const SizedBox(height: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: const TextStyle(
                    fontFamily: 'Montserrat',
                    fontSize: 12,
                    fontWeight: FontWeight.w600,
                    color: Color(0xFF191919),
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  calories,
                  style: const TextStyle(
                    fontFamily: 'Montserrat',
                    fontSize: 10,
                    fontWeight: FontWeight.w500,
                    color: Color(0xFF3A4750),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
