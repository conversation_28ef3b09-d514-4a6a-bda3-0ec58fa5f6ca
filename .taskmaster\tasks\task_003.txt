# Task ID: 3
# Title: Core UI: Navigation, Theming, and Design System
# Status: done
# Dependencies: 1
# Priority: high
# Description: Implement the core application shell, including bottom tab navigation and theming, based on the official Figma designs. This involves setting up navigation, defining light and dark themes with the specified color palette and typography, and creating initial design system components.
# Details:
The primary source of truth for all UI is the Figma document (channel: cx4zzwdw, 43 screens). Use `go_router` to set up a `ShellRoute` for the main app sections: Home, Meal Plans, Exercise, and Profile. Define `ThemeData` for both light and dark modes using the specified color palette (Primary: #00adb5, Text: #191919, Secondary: #3a4750, Background: #fcfcfc) and the 'Montserrat' font family. The recommended approach is to extract Figma components and styles, converting them into reusable Flutter widgets. Ensure all components are theme-aware.

# Test Strategy:
Widget tests to verify navigation between tabs (Home, Meal Plans, Exercise, Profile). Manual testing to switch between light and dark modes on all screens, comparing against the Figma designs for visual consistency. Check for correct font usage and color application.

# Subtasks:
## 1. Implement Home Screen with modular widgets [done]
### Dependencies: None
### Description: Refactored the Home Screen into 8 modular widgets based on Figma design, improving maintainability.
### Details:


## 2. Implement Exercise Screen with modular widgets [done]
### Dependencies: None
### Description: Refactored the Exercise Screen into 3 modular widgets and aligned it with the Figma design.
### Details:


## 3. Implement Profile Screen with modular widgets [done]
### Dependencies: None
### Description: Refactored the Profile Screen into 4 modular widgets based on Figma design.
### Details:


## 4. Implement Meal Plans Screen with modular widgets [done]
### Dependencies: None
### Description: Refactored the Meal Plans Screen into 4 modular widgets, aligned with Figma, and added stateful favorite functionality.
### Details:


