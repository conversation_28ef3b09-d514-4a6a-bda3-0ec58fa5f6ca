import 'package:fit_meal_app/features/auth/services/auth_service.dart';
import 'package:fit_meal_app/features/auth/widgets/custom_text_field.dart';
import 'package:fit_meal_app/shared/widgets/app_toast.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:fit_meal_app/core/utils/size_config.dart';

class UpdatePasswordScreen extends ConsumerStatefulWidget {
  const UpdatePasswordScreen({super.key});

  @override
  ConsumerState<UpdatePasswordScreen> createState() =>
      _UpdatePasswordScreenState();
}

class _UpdatePasswordScreenState extends ConsumerState<UpdatePasswordScreen> {
  final _passwordController = TextEditingController();
  final _confirmPasswordController = TextEditingController();
  bool _isLoading = false;

  @override
  void dispose() {
    _passwordController.dispose();
    _confirmPasswordController.dispose();
    super.dispose();
  }

  Future<void> _updatePassword() async {
    if (_passwordController.text != _confirmPasswordController.text) {
      showAppToast(context: context, message: 'Passwords do not match.');
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      await ref
          .read(authServiceProvider)
          .updatePassword(_passwordController.text.trim());
      if (mounted) {
        showAppToast(
          context: context,
          message: 'Password updated successfully!',
          type: ToastType.success,
        );
        context.go('/login');
      }
    } on AuthException catch (e) {
      if (mounted) {
        showAppToast(context: context, message: e.message);
      }
    } catch (e) {
      if (mounted) {
        showAppToast(
          context: context,
          message: 'An unexpected error occurred.',
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    SizeConfig().init(context);
    return Scaffold(
      backgroundColor: const Color(0xFFFCFCFC),
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        leading: IconButton(
          icon: Image.asset(
            'assets/icons/left-arrow.png',
            width: 16,
            height: 16,
          ),
          onPressed: () => context.go('/login'),
        ),
        title: const Text(
          'Create New Password',
          style: TextStyle(
            fontFamily: 'Montserrat',
            fontSize: 18,
            fontWeight: FontWeight.w600,
            color: Color(0xFF191919),
          ),
        ),
        centerTitle: true,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.symmetric(horizontal: 20.0),
        child: Column(
          children: [
            SizedBox(height: SizeConfig.getPropHeight(40)),
            CustomTextField(
              controller: _passwordController,
              labelText: 'New Password',
              hintText: '******',
              isPassword: true,
            ),
            SizedBox(height: SizeConfig.getPropHeight(20)),
            CustomTextField(
              controller: _confirmPasswordController,
              labelText: 'Confirm Password',
              hintText: '******',
              isPassword: true,
            ),
            SizedBox(height: SizeConfig.getPropHeight(40)),
            ElevatedButton(
              onPressed: _isLoading ? null : _updatePassword,
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFF00ADB5),
                minimumSize: Size(
                  double.infinity,
                  SizeConfig.getPropHeight(55),
                ),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(6),
                ),
              ),
              child:
                  _isLoading
                      ? const CircularProgressIndicator(color: Colors.white)
                      : const Text(
                        'Update Password',
                        style: TextStyle(
                          color: Colors.white,
                          fontFamily: 'Montserrat',
                          fontWeight: FontWeight.w600,
                          fontSize: 18,
                        ),
                      ),
            ),
          ],
        ),
      ),
    );
  }
}
