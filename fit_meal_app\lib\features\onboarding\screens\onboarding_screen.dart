import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import 'select_favorite_screen.dart';
import 'gender_screen.dart';
// We will create these screen files in subsequent tasks
// import 'age_screen.dart';
// import 'weight_screen.dart';
// ... etc

// Main Onboarding Screen that hosts the PageView
class OnboardingScreen extends ConsumerStatefulWidget {
  const OnboardingScreen({super.key});

  @override
  ConsumerState<OnboardingScreen> createState() => _OnboardingScreenState();
}

class _OnboardingScreenState extends ConsumerState<OnboardingScreen> {
  final PageController _pageController = PageController();
  int _currentPage = 0;

  @override
  void initState() {
    super.initState();
    _pageController.addListener(() {
      setState(() {
        _currentPage = _pageController.page!.round();
      });
    });
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  void _nextPage() {
    _pageController.nextPage(
      duration: const Duration(milliseconds: 300),
      curve: Curves.easeInOut,
    );
  }

  @override
  Widget build(BuildContext context) {
    // List of all the onboarding screens/steps
    final List<Widget> onboardingPages = [
      SelectFavoriteScreen(onNext: _nextPage),
      GenderScreen(onNext: _nextPage),
      const Center(child: Text("Screen 3: Age (UI in Task 5.4)")),
      const Center(child: Text("Screen 4: Weight (UI in Task 5.5)")),
      const Center(child: Text("Screen 5: Height (UI in Task 5.6)")),
      const Center(child: Text("Screen 6: Fitness Level (UI in Task 5.7)")),
      const Center(child: Text("Screen 7: Goal (UI in Task 5.8)")),
      const Center(child: Text("Screen 8: Get Started (UI in Task 5.9)")),
    ];

    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        leading: _currentPage == 0
            ? null
            : IconButton(
                icon: const Icon(Icons.arrow_back_ios_new, color: Colors.black),
                onPressed: () {
                  _pageController.previousPage(
                    duration: const Duration(milliseconds: 300),
                    curve: Curves.easeInOut,
                  );
                },
              ),
      ),
      body: PageView(
        controller: _pageController,
        physics: const NeverScrollableScrollPhysics(), // Disable swiping
        children: onboardingPages,
      ),
      // We can add a common 'Next' button here in the future
    );
  }
} 