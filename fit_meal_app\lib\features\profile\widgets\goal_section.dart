import 'package:flutter/material.dart';
import 'package:fit_meal_app/features/home/<USER>/category_section.dart'; // Re-using CategoryItem

class GoalSection extends StatelessWidget {
  const GoalSection({super.key});

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Goal',
          style: TextStyle(
            fontFamily: 'Montserrat',
            fontSize: 16,
            fontWeight: FontWeight.w700,
            color: Color(0xFF191919),
          ),
        ),
        const SizedBox(height: 16),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceAround,
          children: const [
            CategoryItem(iconPath: 'assets/icons/yoga.png', label: 'Yoga'),
            CategoryItem(iconPath: 'assets/icons/gym.png', label: 'Gym'),
            CategoryItem(iconPath: 'assets/icons/cardio.png', label: 'Cardio'),
            CategoryItem(
              iconPath: 'assets/icons/stretch.png',
              label: 'Stretch',
            ),
          ],
        ),
      ],
    );
  }
}
