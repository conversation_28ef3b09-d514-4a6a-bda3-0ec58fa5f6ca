name: fit_meal_app
description: "FitMeal - A comprehensive fitness and meal planning app."
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.0+1

environment:
  sdk: ^3.7.2

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter

  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^1.0.8

  # State Management
  flutter_riverpod: ^2.6.1
  
  # Backend & Database
  supabase_flutter: ^2.9.0
  
  # Navigation
  go_router: ^16.0.0
  
  # Local Storage
  hive: ^2.2.3
  hive_flutter: ^1.1.0
  
  # Firebase
  firebase_core: ^3.12.0
  firebase_auth: ^5.3.3
  firebase_messaging: ^15.1.6
  
  # In-App Purchases
  purchases_flutter: ^8.10.4
  
  # Image Caching & UI
  cached_network_image: ^3.4.1
  
  # HTTP & Networking
  http: ^1.2.2
  
  # JSON Serialization
  json_annotation: ^4.9.0
  
  # Utilities
  intl: ^0.20.2
  shared_preferences: ^2.3.3
  permission_handler: ^12.0.1
  email_validator: ^3.0.0
  pinput: ^5.0.1
  fluttertoast: ^8.2.6
  flutter_dotenv: ^5.1.0
  country_picker: ^2.0.26
  flutter_libphonenumber: ^2.5.1

  # Google Sign-In
  google_sign_in: ^6.3.0
  
  # Fonts
  google_fonts: ^6.1.0

dev_dependencies:
  flutter_test:
    sdk: flutter

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^6.0.0
  
  # Hive code generation
  hive_generator: ^2.0.1
  build_runner: ^2.4.9
  
  # JSON code generation
  json_serializable: ^6.8.0

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  assets:
    - .env
    - assets/images/
    - assets/icons/

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/to/resolution-aware-images

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/to/asset-from-package

  # Custom fonts have been replaced with Google Fonts for better performance
  # Original font configuration backed up below (commented out):
  # fonts:
  #   - family: Montserrat
  #     fonts:
  #       - asset: assets/fonts/Montserrat-Regular.ttf
  #       - asset: assets/fonts/Montserrat-Medium.ttf
  #         weight: 500
  #       - asset: assets/fonts/Montserrat-SemiBold.ttf
  #         weight: 600
  #       - asset: assets/fonts/Montserrat-Bold.ttf
  #         weight: 700
  #   - family: Poppins
  #     fonts:
  #       - asset: assets/fonts/Poppins-Regular.ttf
  #       - asset: assets/fonts/Poppins-Medium.ttf
  #         weight: 500
  #       - asset: assets/fonts/Poppins-SemiBold.ttf
  #         weight: 600
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/to/font-from-package
