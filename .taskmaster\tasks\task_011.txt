# Task ID: 11
# Title: <PERSON><PERSON><PERSON><PERSON> <PERSON> (Schedule)
# Status: pending
# Dependencies: 2, 3, 4, 6, 7
# Priority: low
# Description: Implement the Schedule screen, allowing users to view a calendar and plan their workouts and meals for specific dates.
# Details:
This task involves creating a new 'Schedule' screen accessible via the main navigation. A new Supabase table, `user_schedule`, will be created with columns for `user_id`, `item_id`, `item_type` ('workout' or 'meal'), and `scheduled_date`, with appropriate RLS policies to ensure users can only access their own schedule. The UI will feature a calendar view (using a library like `table_calendar`) to select dates. A Riverpod provider will fetch scheduled items for the selected date from the new table. Users should be able to add workouts or meals to the schedule, which will likely involve navigating to the respective lists (from Task 6 & 7), selecting an item, and assigning it to a date.

# Test Strategy:
1. **Backend**: Write a SQL test in Supabase to verify the RLS policy on the `user_schedule` table, ensuring users cannot read or write schedule entries for other users. 2. **Widget Testing**: Create widget tests for the Schedule screen using a mock Riverpod provider. Verify that the calendar renders correctly, events are displayed for the selected day, and the UI correctly handles loading, error, and empty states. 3. **Integration Testing**: Write a test to simulate the full flow: a user navigates to the schedule, selects a future date, adds a workout, and verifies that the workout now appears on the calendar for that date.
