import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../providers/onboarding_provider.dart';

class GenderScreen extends ConsumerStatefulWidget {
  final VoidCallback onNext;
  final VoidCallback onBack;

  const GenderScreen({
    super.key,
    required this.onNext,
    required this.onBack,
  });

  @override
  ConsumerState<GenderScreen> createState() => _GenderScreenState();
}

class _GenderScreenState extends ConsumerState<GenderScreen> {
  String? selectedGender;

  @override
  void initState() {
    super.initState();
    // Initialize with existing gender from provider if available
    final currentGender = ref.read(onboardingProvider).gender;
    if (currentGender != null) {
      selectedGender = currentGender;
    }
  }

  void _selectGender(String gender) {
    setState(() {
      selectedGender = gender;
    });
    
    // Update the provider with selected gender
    ref.read(onboardingProvider.notifier).updateGender(gender);
  }

  void _handleNext() {
    // Only proceed if a gender is selected
    if (selectedGender != null) {
      widget.onNext();
    }
  }

  void _handleSkip() {
    // Clear selection and proceed
    selectedGender = null;
    ref.read(onboardingProvider.notifier).updateGender('');
    widget.onNext();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFFCFCFC),
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 18),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const SizedBox(height: 12),

              // Header with back button and skip
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  IconButton(
                    icon: const Icon(
                      Icons.arrow_back_ios_new,
                      color: Colors.black,
                      size: 20,
                    ),
                    onPressed: widget.onBack,
                  ),
                  GestureDetector(
                    onTap: _handleSkip,
                    child: const Text(
                      'Skip',
                      style: TextStyle(
                        fontFamily: 'Montserrat',
                        fontSize: 14,
                        fontWeight: FontWeight.w600,
                        color: Color(0xFF00ADB5),
                      ),
                    ),
                  ),
                ],
              ),

              const SizedBox(height: 12),
              
              // Step indicator
              const Text(
                'Step 2 of 8',
                style: TextStyle(
                  fontFamily: 'Montserrat',
                  fontSize: 12,
                  fontWeight: FontWeight.w500,
                  color: Color(0xFF191919),
                ),
              ),
              
              const SizedBox(height: 8),
              
              // Main heading
              const Text(
                "What's your gender?",
                style: TextStyle(
                  fontFamily: 'Montserrat',
                  fontSize: 20,
                  fontWeight: FontWeight.w600,
                  color: Color(0xFF191919),
                ),
              ),
              
              const SizedBox(height: 40),
              
              // Gender options
              Expanded(
                child: Column(
                  children: [
                    // Male option
                    GenderCard(
                      gender: 'Male',
                      icon: Icons.male,
                      isSelected: selectedGender == 'Male',
                      onTap: () => _selectGender('Male'),
                    ),
                    
                    const SizedBox(height: 16),
                    
                    // Female option
                    GenderCard(
                      gender: 'Female',
                      icon: Icons.female,
                      isSelected: selectedGender == 'Female',
                      onTap: () => _selectGender('Female'),
                    ),
                    
                    const Spacer(),
                  ],
                ),
              ),
              
              const SizedBox(height: 20),
              
              // Next button
              SizedBox(
                width: double.infinity,
                height: 55,
                child: ElevatedButton(
                  onPressed: selectedGender != null ? _handleNext : null,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: selectedGender != null 
                        ? const Color(0xFF00ADB5) 
                        : const Color(0xFFE5E5E5),
                    foregroundColor: Colors.white,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(6),
                    ),
                    elevation: 0,
                  ),
                  child: Text(
                    'Next Steps',
                    style: TextStyle(
                      fontFamily: 'Montserrat',
                      fontSize: 18,
                      fontWeight: FontWeight.w600,
                      color: selectedGender != null 
                          ? Colors.white 
                          : const Color(0xFF9E9E9E),
                    ),
                  ),
                ),
              ),
              
              const SizedBox(height: 30),
            ],
          ),
        ),
      ),
    );
  }
}

// Custom widget for gender selection cards
class GenderCard extends StatelessWidget {
  final String gender;
  final IconData icon;
  final bool isSelected;
  final VoidCallback onTap;

  const GenderCard({
    super.key,
    required this.gender,
    required this.icon,
    required this.isSelected,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        width: double.infinity,
        height: 60,
        decoration: BoxDecoration(
          color: isSelected ? const Color(0xFF00ADB5) : Colors.white,
          borderRadius: BorderRadius.circular(6),
          border: Border.all(
            color: isSelected ? const Color(0xFF00ADB5) : const Color(0xFFE5E5E5),
            width: 1,
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.05),
              blurRadius: 10,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: Row(
            children: [
              // Icon
              Icon(
                icon,
                size: 24,
                color: isSelected ? Colors.white : const Color(0xFF303841),
              ),
              
              const SizedBox(width: 12),
              
              // Text
              Expanded(
                child: Text(
                  gender,
                  style: TextStyle(
                    fontFamily: 'Montserrat',
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                    color: isSelected ? Colors.white : const Color(0xFF303841),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
