import 'package:flutter/material.dart';

class HeaderSection extends StatelessWidget {
  final VoidCallback onBackPressed;
  final VoidCallback onFilterPressed;

  const HeaderSection({
    super.key,
    required this.onBackPressed,
    required this.onFilterPressed,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          // Back button
          GestureDetector(
            onTap: onBackPressed,
            child: Container(
              width: 24,
              height: 24,
              alignment: Alignment.center,
              child: Image.asset(
                'assets/icons/left-arrow.png',
                width: 16,
                height: 16,
              ),
            ),
          ),

          // Title
          const Text(
            'Meal plan',
            style: TextStyle(
              fontFamily: 'Montserrat',
              fontWeight: FontWeight.w600,
              fontSize: 18,
              color: Color(0xFF191919),
              letterSpacing: 0,
              height: 1.22,
            ),
          ),

          // Filter button
          GestureDetector(
            onTap: onFilterPressed,
            child: Container(
              width: 20,
              height: 20,
              alignment: Alignment.center,
              child: Image.asset(
                'assets/icons/filter-btn.png',
                width: 20,
                height: 20,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
