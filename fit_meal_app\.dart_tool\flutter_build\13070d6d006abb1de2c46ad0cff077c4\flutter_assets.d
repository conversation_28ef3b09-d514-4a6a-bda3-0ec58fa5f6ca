 D:\\FitMeal\\fit_meal_app\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\.env D:\\FitMeal\\fit_meal_app\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets\\images\\jumping.png D:\\FitMeal\\fit_meal_app\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets\\images\\sitting.png D:\\FitMeal\\fit_meal_app\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets\\icons\\Calendar.png D:\\FitMeal\\fit_meal_app\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets\\icons\\carbs.png D:\\FitMeal\\fit_meal_app\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets\\icons\\cardio.png D:\\FitMeal\\fit_meal_app\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets\\icons\\cycling.png D:\\FitMeal\\fit_meal_app\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets\\icons\\diet.png D:\\FitMeal\\fit_meal_app\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets\\icons\\edit-btn.png D:\\FitMeal\\fit_meal_app\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets\\icons\\exercise.png D:\\FitMeal\\fit_meal_app\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets\\icons\\facebook.png D:\\FitMeal\\fit_meal_app\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets\\icons\\fat.png D:\\FitMeal\\fit_meal_app\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets\\icons\\filter-btn.png D:\\FitMeal\\fit_meal_app\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets\\icons\\fullbody.png D:\\FitMeal\\fit_meal_app\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets\\icons\\google.png D:\\FitMeal\\fit_meal_app\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets\\icons\\gym.png D:\\FitMeal\\fit_meal_app\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets\\icons\\health.png D:\\FitMeal\\fit_meal_app\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets\\icons\\home.png D:\\FitMeal\\fit_meal_app\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets\\icons\\john.png D:\\FitMeal\\fit_meal_app\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets\\icons\\left-arrow.png D:\\FitMeal\\fit_meal_app\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets\\icons\\mealplan.png D:\\FitMeal\\fit_meal_app\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets\\icons\\menu_icon.png D:\\FitMeal\\fit_meal_app\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets\\icons\\Notification_icon.png D:\\FitMeal\\fit_meal_app\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets\\icons\\profile.png D:\\FitMeal\\fit_meal_app\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets\\icons\\protein.png D:\\FitMeal\\fit_meal_app\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets\\icons\\right-arrow.png D:\\FitMeal\\fit_meal_app\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets\\icons\\running.png D:\\FitMeal\\fit_meal_app\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets\\icons\\stretch.png D:\\FitMeal\\fit_meal_app\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets\\icons\\Vector.png D:\\FitMeal\\fit_meal_app\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets\\icons\\walking.png D:\\FitMeal\\fit_meal_app\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets\\icons\\yoga.png D:\\FitMeal\\fit_meal_app\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets\\icons\\yogas.png D:\\FitMeal\\fit_meal_app\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\packages\\fluttertoast\\assets\\toastify.css D:\\FitMeal\\fit_meal_app\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\packages\\fluttertoast\\assets\\toastify.js D:\\FitMeal\\fit_meal_app\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\packages\\cupertino_icons\\assets\\CupertinoIcons.ttf D:\\FitMeal\\fit_meal_app\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\fonts\\MaterialIcons-Regular.otf D:\\FitMeal\\fit_meal_app\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\shaders\\ink_sparkle.frag D:\\FitMeal\\fit_meal_app\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\AssetManifest.json D:\\FitMeal\\fit_meal_app\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\AssetManifest.bin D:\\FitMeal\\fit_meal_app\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\FontManifest.json D:\\FitMeal\\fit_meal_app\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\NOTICES.Z D:\\FitMeal\\fit_meal_app\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\NativeAssetsManifest.json:  D:\\FitMeal\\fit_meal_app\\pubspec.yaml D:\\FitMeal\\fit_meal_app\\.env D:\\FitMeal\\fit_meal_app\\assets\\images\\jumping.png D:\\FitMeal\\fit_meal_app\\assets\\images\\sitting.png D:\\FitMeal\\fit_meal_app\\assets\\icons\\Calendar.png D:\\FitMeal\\fit_meal_app\\assets\\icons\\carbs.png D:\\FitMeal\\fit_meal_app\\assets\\icons\\cardio.png D:\\FitMeal\\fit_meal_app\\assets\\icons\\cycling.png D:\\FitMeal\\fit_meal_app\\assets\\icons\\diet.png D:\\FitMeal\\fit_meal_app\\assets\\icons\\edit-btn.png D:\\FitMeal\\fit_meal_app\\assets\\icons\\exercise.png D:\\FitMeal\\fit_meal_app\\assets\\icons\\facebook.png D:\\FitMeal\\fit_meal_app\\assets\\icons\\fat.png D:\\FitMeal\\fit_meal_app\\assets\\icons\\filter-btn.png D:\\FitMeal\\fit_meal_app\\assets\\icons\\fullbody.png D:\\FitMeal\\fit_meal_app\\assets\\icons\\google.png D:\\FitMeal\\fit_meal_app\\assets\\icons\\gym.png D:\\FitMeal\\fit_meal_app\\assets\\icons\\health.png D:\\FitMeal\\fit_meal_app\\assets\\icons\\home.png D:\\FitMeal\\fit_meal_app\\assets\\icons\\john.png D:\\FitMeal\\fit_meal_app\\assets\\icons\\left-arrow.png D:\\FitMeal\\fit_meal_app\\assets\\icons\\mealplan.png D:\\FitMeal\\fit_meal_app\\assets\\icons\\menu_icon.png D:\\FitMeal\\fit_meal_app\\assets\\icons\\Notification_icon.png D:\\FitMeal\\fit_meal_app\\assets\\icons\\profile.png D:\\FitMeal\\fit_meal_app\\assets\\icons\\protein.png D:\\FitMeal\\fit_meal_app\\assets\\icons\\right-arrow.png D:\\FitMeal\\fit_meal_app\\assets\\icons\\running.png D:\\FitMeal\\fit_meal_app\\assets\\icons\\stretch.png D:\\FitMeal\\fit_meal_app\\assets\\icons\\Vector.png D:\\FitMeal\\fit_meal_app\\assets\\icons\\walking.png D:\\FitMeal\\fit_meal_app\\assets\\icons\\yoga.png D:\\FitMeal\\fit_meal_app\\assets\\icons\\yogas.png C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\fluttertoast-8.2.12\\assets\\toastify.css C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\fluttertoast-8.2.12\\assets\\toastify.js C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\cupertino_icons-1.0.8\\assets\\CupertinoIcons.ttf C:\\Users\\<USER>\\flutter\\bin\\cache\\artifacts\\material_fonts\\MaterialIcons-Regular.otf C:\\Users\\<USER>\\flutter\\packages\\flutter\\lib\\src\\material\\shaders\\ink_sparkle.frag D:\\FitMeal\\fit_meal_app\\.dart_tool\\flutter_build\\13070d6d006abb1de2c46ad0cff077c4\\native_assets.json C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\_fe_analyzer_shared-67.0.0\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\_flutterfire_internals-1.3.57\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\analyzer-6.4.1\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\app_links-6.4.0\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\app_links_linux-1.0.3\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\app_links_platform_interface-2.0.2\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\app_links_web-1.0.4\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\args-2.7.0\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\async-2.13.0\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\boolean_selector-2.1.2\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\build-2.4.1\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\build_config-1.1.2\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\build_daemon-4.0.4\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\build_resolvers-2.4.2\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\build_runner-2.4.13\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\build_runner_core-7.3.2\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\built_collection-5.1.1\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\built_value-8.10.1\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\cached_network_image-3.4.1\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\cached_network_image_platform_interface-4.1.1\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\cached_network_image_web-1.3.1\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\characters-1.4.0\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\checked_yaml-2.0.4\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\clock-1.1.2\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\code_builder-4.10.1\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\collection-1.19.1\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\convert-3.1.2\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\country_picker-2.0.27\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\crypto-3.0.6\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\cupertino_icons-1.0.8\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\dart_style-2.3.6\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\email_validator-3.0.0\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\fake_async-1.3.3\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\ffi-2.1.4\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\file-7.0.1\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\firebase_auth-5.6.1\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\firebase_auth_platform_interface-7.7.1\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\firebase_auth_web-5.15.1\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\firebase_core-3.15.0\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\firebase_core_platform_interface-5.4.1\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\firebase_core_web-2.24.0\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\firebase_messaging-15.2.8\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\firebase_messaging_platform_interface-4.6.8\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\firebase_messaging_web-3.10.8\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\fixnum-1.1.1\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_cache_manager-3.4.1\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_dotenv-5.2.1\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_libphonenumber-2.5.1\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_libphonenumber_android-1.4.1\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_libphonenumber_ios-1.4.0\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_libphonenumber_platform_interface-2.1.0\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_libphonenumber_web-1.3.0\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_lints-6.0.0\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_riverpod-2.6.1\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\fluttertoast-8.2.12\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\freezed_annotation-2.4.4\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\frontend_server_client-4.0.0\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\functions_client-2.4.3\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\glob-2.1.3\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\go_router-16.0.0\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\google_fonts-6.2.1\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\google_identity_services_web-0.3.3+1\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\google_sign_in-6.3.0\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\google_sign_in_android-6.2.1\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\google_sign_in_ios-5.9.0\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\google_sign_in_platform_interface-2.5.0\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\google_sign_in_web-0.12.4+4\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\gotrue-2.13.0\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\graphs-2.3.2\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\gtk-2.1.0\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\hive-2.2.3\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\hive_flutter-1.1.0\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\hive_generator-2.0.1\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\http-1.4.0\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\http_multi_server-3.2.2\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\http_parser-4.1.2\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\intl-0.20.2\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\io-1.0.5\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\js-0.7.2\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\json_annotation-4.9.0\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\json_serializable-6.8.0\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\jwt_decode-0.3.1\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\leak_tracker-11.0.1\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\leak_tracker_flutter_testing-3.0.10\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\leak_tracker_testing-3.0.2\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\lints-6.0.0\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\logging-1.3.0\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\matcher-0.12.17\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\material_color_utilities-0.11.1\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\meta-1.16.0\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\mime-2.0.0\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\octo_image-2.1.0\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\package_config-2.2.0\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\path-1.9.1\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\path_provider-2.1.5\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\path_provider_android-2.2.17\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\path_provider_foundation-2.4.1\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\path_provider_linux-2.2.1\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\path_provider_platform_interface-2.1.2\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\path_provider_windows-2.3.0\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\permission_handler-12.0.1\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\permission_handler_android-13.0.1\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\permission_handler_apple-9.4.7\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\permission_handler_html-0.1.3+5\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\permission_handler_platform_interface-4.3.0\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\permission_handler_windows-0.2.1\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\pinput-5.0.1\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\platform-3.1.6\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\plugin_platform_interface-2.1.8\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\pool-1.5.1\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\postgrest-2.4.2\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\pub_semver-2.2.0\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\pubspec_parse-1.5.0\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\purchases_flutter-8.10.5\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\realtime_client-2.5.1\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\retry-3.1.2\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\riverpod-2.6.1\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\shared_preferences-2.5.3\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\shared_preferences_android-2.4.10\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\shared_preferences_foundation-2.5.4\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\shared_preferences_linux-2.4.1\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\shared_preferences_platform_interface-2.4.1\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\shared_preferences_web-2.4.3\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\shared_preferences_windows-2.4.1\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\shelf-1.4.2\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\shelf_web_socket-2.0.1\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\source_gen-1.5.0\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\source_helper-1.3.5\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\source_span-1.10.1\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\sprintf-7.0.0\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\sqflite-2.4.2\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\sqflite_android-2.4.1\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\sqflite_common-2.5.5\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\sqflite_darwin-2.4.2\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\sqflite_platform_interface-2.4.0\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\stack_trace-1.12.1\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\state_notifier-1.0.0\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\storage_client-2.4.0\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\stream_channel-2.1.4\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\stream_transform-2.1.1\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\string_scanner-1.4.1\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\supabase-2.8.0\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\supabase_flutter-2.9.1\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\synchronized-3.4.0\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\term_glyph-1.2.2\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\test_api-0.7.6\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\timing-1.0.2\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\typed_data-1.4.0\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\universal_io-2.2.2\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\universal_platform-1.1.0\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\url_launcher-6.3.1\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\url_launcher_android-6.3.16\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\url_launcher_ios-6.3.3\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\url_launcher_linux-3.2.1\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\url_launcher_macos-3.2.2\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\url_launcher_platform_interface-2.3.2\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\url_launcher_web-2.4.1\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\url_launcher_windows-3.1.4\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\uuid-4.5.1\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\vector_math-2.2.0\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\vm_service-15.0.2\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\watcher-1.1.2\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\web-1.1.1\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\web_socket-1.0.1\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\web_socket_channel-3.0.3\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\xdg_directories-1.1.0\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\yaml-3.1.3\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\yet_another_json_isolate-2.1.0\\LICENSE C:\\Users\\<USER>\\flutter\\bin\\cache\\pkg\\sky_engine\\LICENSE C:\\Users\\<USER>\\flutter\\packages\\flutter\\LICENSE D:\\FitMeal\\fit_meal_app\\DOES_NOT_EXIST_RERUN_FOR_WILDCARD248573510