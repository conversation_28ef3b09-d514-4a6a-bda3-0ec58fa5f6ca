# Task ID: 15
# Title: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (Caching)
# Status: pending
# Dependencies: 1, 2, 3, 6, 7
# Priority: low
# Description: Implement a local caching layer using Hive to store essential application data, such as workout and meal details, ensuring users can access this information while offline.
# Details:
This task involves integrating Hive for local data persistence. First, ensure Hive is initialized in the `main()` function using `await Hive.initFlutter()`. Modify the data models for workouts and meals to support Hive by adding `@HiveType` annotations to the classes and `@HiveField` to their properties. Generate the necessary TypeAdapters by running `flutter pub run build_runner build`. The core of this task is to update the data fetching logic within the Riverpod providers (or repositories). The new strategy should be 'cache-then-network': first, attempt to display data from the local Hive box for instant UI loading. Concurrently, trigger a fetch from the Supabase API. If the API call is successful, update the Hive box with the fresh data and refresh the UI. If the API call fails (e.g., no network), the app will continue to function with the stale data from the cache. Open Hive boxes for each data type, such as `Hive.openBox<Workout>('workouts')` and `Hive.openBox<Meal>('meals')`, to store the respective data.

# Test Strategy:
1. **Unit/Widget Testing**: Mock the Hive `Box` interface. Create tests for the repository/provider layer to verify the caching logic. Test the 'offline' scenario by mocking a network error from the Supabase client and asserting that the provider correctly falls back to returning data from the mock Hive box. Test the 'online' scenario by mocking a successful API response and verifying that the provider not only returns the new data but also calls the `put` or `putAll` method on the mock Hive box. 2. **Manual Integration Testing**: Run the app with an active internet connection. Navigate to the workout and meal list/detail screens to ensure the cache is populated. Then, disable all network connectivity (Wi-Fi and mobile data) on the test device. Relaunch the application and navigate to the same screens. Verify that the workout and meal data is still displayed correctly from the cache and that the app remains usable.
