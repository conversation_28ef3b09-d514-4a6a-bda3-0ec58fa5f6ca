import 'package:flutter/material.dart';
import 'widgets/header_section.dart';
import 'widgets/date_picker_section.dart';
import 'widgets/tab_section.dart';
import 'widgets/meals_list_section.dart';

class MealPlansScreen extends StatefulWidget {
  const MealPlansScreen({super.key});

  @override
  State<MealPlansScreen> createState() => _MealPlansScreenState();
}

class _MealPlansScreenState extends State<MealPlansScreen> {
  String selectedTab = 'Breakfast';

  void _onTabChanged(String tab) {
    setState(() {
      selectedTab = tab;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFFCFCFC),
      body: SafeArea(
        child: Column(
          children: [
            // Header Section
            HeaderSection(
              onBackPressed: () => Navigator.of(context).pop(),
              onFilterPressed: () {
                // Filter functionality
              },
            ),

            // Date Picker Section
            const DatePickerSection(),

            // Tab Section
            TabSection(selectedTab: selectedTab, onTabChanged: _onTabChanged),

            // Meals List Section
            Expanded(child: MealsListSection(selectedTab: selectedTab)),
          ],
        ),
      ),
    );
  }
}
