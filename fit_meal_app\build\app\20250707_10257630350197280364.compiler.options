"-Xallow-no-source-files" "-classpath" "D:\\FitMeal\\fit_meal_app\\build\\app\\intermediates\\compile_and_runtime_not_namespaced_r_class_jar\\debug\\processDebugResources\\R.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\f02e314428da85751f55e56cd0ad4b95\\transformed\\jetified-libs.jar;D:\\FitMeal\\fit_meal_app\\build\\shared_preferences_android\\intermediates\\compile_library_classes_jar\\debug\\bundleLibCompileToJarDebug\\classes.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\3255c646f37bc110bf08465db625e514\\transformed\\jetified-kotlin-stdlib-jdk7-1.8.22.jar;D:\\FitMeal\\fit_meal_app\\build\\app_links\\intermediates\\compile_library_classes_jar\\debug\\bundleLibCompileToJarDebug\\classes.jar;D:\\FitMeal\\fit_meal_app\\build\\firebase_auth\\intermediates\\compile_library_classes_jar\\debug\\bundleLibCompileToJarDebug\\classes.jar;D:\\FitMeal\\fit_meal_app\\build\\firebase_messaging\\intermediates\\compile_library_classes_jar\\debug\\bundleLibCompileToJarDebug\\classes.jar;D:\\FitMeal\\fit_meal_app\\build\\firebase_core\\intermediates\\compile_library_classes_jar\\debug\\bundleLibCompileToJarDebug\\classes.jar;D:\\FitMeal\\fit_meal_app\\build\\flutter_libphonenumber_android\\intermediates\\compile_library_classes_jar\\debug\\bundleLibCompileToJarDebug\\classes.jar;D:\\FitMeal\\fit_meal_app\\build\\fluttertoast\\intermediates\\compile_library_classes_jar\\debug\\bundleLibCompileToJarDebug\\classes.jar;D:\\FitMeal\\fit_meal_app\\build\\google_sign_in_android\\intermediates\\compile_library_classes_jar\\debug\\bundleLibCompileToJarDebug\\classes.jar;D:\\FitMeal\\fit_meal_app\\build\\path_provider_android\\intermediates\\compile_library_classes_jar\\debug\\bundleLibCompileToJarDebug\\classes.jar;D:\\FitMeal\\fit_meal_app\\build\\permission_handler_android\\intermediates\\compile_library_classes_jar\\debug\\bundleLibCompileToJarDebug\\classes.jar;D:\\FitMeal\\fit_meal_app\\build\\purchases_flutter\\intermediates\\compile_library_classes_jar\\debug\\bundleLibCompileToJarDebug\\classes.jar;D:\\FitMeal\\fit_meal_app\\build\\sqflite_android\\intermediates\\compile_library_classes_jar\\debug\\bundleLibCompileToJarDebug\\classes.jar;D:\\FitMeal\\fit_meal_app\\build\\url_launcher_android\\intermediates\\compile_library_classes_jar\\debug\\bundleLibCompileToJarDebug\\classes.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\7dc78fd24a2e8b1cd43eef8c8b6162c5\\transformed\\jetified-flutter_embedding_debug-1.0.0-2f2baae820737cd435e93f6acee33dc2694305a8.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\b5d796884acfb5221320f6a710eefe5b\\transformed\\fragment-1.7.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\0635fd5604f65f111ec61acff26bc578\\transformed\\jetified-activity-1.8.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\c1c12152427429ae30e971d880310dd2\\transformed\\loader-1.1.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\6327214f7424f435d5aecdf02bf7f719\\transformed\\lifecycle-viewmodel-2.7.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\fc20c7a21c99c2eeadf5ee4a0341d946\\transformed\\lifecycle-livedata-core-2.7.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\d54de69a2aadaad851dd67515a2590e0\\transformed\\jetified-lifecycle-viewmodel-savedstate-2.7.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\e0228cff99dd7a2a83dc03df3cb086bd\\transformed\\jetified-core-ktx-1.13.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\27c6781ce5b5e5dd555961ea00daef87\\transformed\\viewpager-1.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\2bf32455f78a244626467f1bf9669501\\transformed\\customview-1.1.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\9dd71d5502c79ef1ec770ab30cc18b46\\transformed\\core-1.13.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\daf0e82d0b9e5930e3de7b32a235421c\\transformed\\lifecycle-runtime-2.7.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\915497d53c68924f7e99269e83d15781\\transformed\\jetified-lifecycle-process-2.7.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\androidx.lifecycle\\lifecycle-common-java8\\2.7.0\\2ad14aed781c4a73ed4dbb421966d408a0a06686\\lifecycle-common-java8-2.7.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\androidx.lifecycle\\lifecycle-common\\2.7.0\\85334205d65cca70ed0109c3acbd29e22a2d9cb1\\lifecycle-common-2.7.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\803cb887565f36b08c24a5da31dcb1f5\\transformed\\jetified-window-1.2.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\fc0ffb14f37f5296085981c347999105\\transformed\\jetified-window-java-1.2.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\8e342aae12b8f6216f58c40e548107c8\\transformed\\jetified-annotation-experimental-1.4.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\6358cd579cd7a1437fa6df276d5bc1dc\\transformed\\jetified-savedstate-1.2.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\androidx.arch.core\\core-common\\2.2.0\\5e1b8b81dfd5f52c56a8d53b18ca759c19a301f3\\core-common-2.2.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\ae0803349eb4278e0823d233f49b1edf\\transformed\\versionedparcelable-1.1.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\androidx.collection\\collection\\1.1.0\\1f27220b47669781457de0d600849a5de0e89909\\collection-1.1.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\e8d9ae175cb8ccd3ecf72cfef965ba95\\transformed\\jetified-annotation-jvm-1.9.1.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\f4b2b1a94b3c8c656ed12fed85c1f402\\transformed\\jetified-kotlinx-coroutines-android-1.7.3.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\1d6bfb0e6b61447017a956013c45397f\\transformed\\jetified-kotlinx-coroutines-core-jvm-1.7.3.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\9f59f32bfcfb63e42d5a18ef149022f4\\transformed\\jetified-kotlin-stdlib-1.9.24.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\162cb9890bb415e9be7413e05602b3d8\\transformed\\jetified-kotlin-stdlib-jdk8-1.8.22.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\6b61b6a8b342f54c05db37a132bf3b39\\transformed\\jetified-armeabi_v7a_debug-1.0.0-2f2baae820737cd435e93f6acee33dc2694305a8.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\354ee71246d5aa0acca467f1b7e50109\\transformed\\jetified-arm64_v8a_debug-1.0.0-2f2baae820737cd435e93f6acee33dc2694305a8.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\5d3dab1345dd45f2fa8d7c39e1e7d7f6\\transformed\\jetified-x86_64_debug-1.0.0-2f2baae820737cd435e93f6acee33dc2694305a8.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\fb69820ca96bcd8cd097d7174a13b45c\\transformed\\jetified-annotations-23.0.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\b3ab0ad990fdd28f713dd3c17dc1ddaa\\transformed\\jetified-startup-runtime-1.1.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\80349c2ed6c3110bee49f40ea0537bc0\\transformed\\jetified-tracing-1.2.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\19b9c2635d7e50b0cc11c79510e6f5c4\\transformed\\jetified-relinker-1.4.5-api.jar;C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\platforms\\android-36\\android.jar;C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\build-tools\\34.0.0\\core-lambda-stubs.jar" "-d" "D:\\FitMeal\\fit_meal_app\\build\\app\\tmp\\kotlin-classes\\debug" "-jvm-target" "11" "-module-name" "app_debug" "-no-jdk" "-no-reflect" "-no-stdlib" "D:\\FitMeal\\fit_meal_app\\android\\app\\src\\main\\kotlin\\com\\fitmeal\\app\\MainActivity.kt" "D:\\FitMeal\\fit_meal_app\\android\\app\\src\\main\\java\\io\\flutter\\plugins\\GeneratedPluginRegistrant.java"